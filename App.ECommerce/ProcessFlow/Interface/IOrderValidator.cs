using App.ECommerce.ProcessFlow.Models;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Units;
using App.ECommerce.Units.Abstractions.Entities;
using App.ECommerce.Units.Enums;

using Microsoft.Extensions.Localization;

namespace App.ECommerce.ProcessFlow.Interface;

public interface IOrderValidator
{
    Result<bool> ValidateItems(string? userId, List<ItemsOrder> items, List<Items> itemsList);
    Result<bool> ValidateVoucherPromotion(User user, List<string> groupNames, List<string> voucherIds,
        List<ItemsOrder> items, List<Items> itemsList, List<Voucher> vouchers, List<String> voucherCodes);
    Result<bool> ValidateVoucherTransport(User user, List<string> groupNames, List<string> voucherIds,
        List<ItemsOrder> items, List<Items> itemsList, List<Voucher> vouchers, List<String> voucherCodes);
    Result<Shop> ValidateShop(string shopId, string partnerId);
    Result<TypeTransportService> ValidateTransportService(Cart cart);
    Result<bool> ValidateOrderCalculation(Cart cart, OrderCalculationModel calculation);
}