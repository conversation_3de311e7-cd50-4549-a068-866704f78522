using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Resource.Dtos;
using App.ECommerce.Units.Abstractions.Entities;
using App.ECommerce.Units.Enums;
using App.ECommerce.ProcessFlow.Interface;
using AutoMapper;
using Microsoft.Extensions.Caching.Memory;
using App.ECommerce.Helpers;
using App.ECommerce.Helpers.Interface;
using App.ECommerce.Resource.Dtos.Webhooks;
using App.ECommerce.Resource.Dtos.NhanhDtos;
using log4net;

namespace App.ECommerce.ProcessFlow.Implement;

public class SyncServiceFlow : ISyncServiceFlow
{
    private readonly ILog _log = log4net.LogManager.GetLogger(typeof(SyncServiceFlow));
    protected readonly IItemsRepository _itemsRepository;
    protected readonly ISyncServiceConfigRepository _syncConfigRepository;
    protected readonly INhanhHelper _nhanhHelper;
    protected readonly ISapoHelper _sapoHelper;
    protected readonly IKiotVietHelper _kiotVietHelper;
    protected readonly IOdooHelper _odooHelper;

    public SyncServiceFlow(
        IItemsRepository itemsRepository,
        ISyncServiceConfigRepository syncConfigRepository,
        INhanhHelper nhanhHelper,
        ISapoHelper sapoHelper,
        IKiotVietHelper kiotVietHelper,
        IOdooHelper odooHelper)
    {
        _itemsRepository = itemsRepository;
        _syncConfigRepository = syncConfigRepository;
        _nhanhHelper = nhanhHelper;
        _sapoHelper = sapoHelper;
        _kiotVietHelper = kiotVietHelper;
        _odooHelper = odooHelper;
    }

    public async Task<Result<SyncServiceConfig>> SaveConfig(SyncServiceConfigDto dto)
    {
        
        return dto.SyncService switch
        {
            SyncServiceEnum.NhanhVN => await _nhanhHelper.SaveNhanhConfig(dto),
            SyncServiceEnum.Sapo => await _sapoHelper.SaveSapoConfig(dto),
            SyncServiceEnum.KiotViet => await _kiotVietHelper.SaveKiotVietConfig(dto),
            SyncServiceEnum.Odoo => await _odooHelper.SaveOdooConfig(dto),
            _ => Result<SyncServiceConfig>.Failure("SYNC_SERVICE_NOT_SUPPORTED")
        };
    }

    public async Task<SyncServiceConfig> GetConfig(SyncServiceEnum syncService, string shopId)
    {
        return syncService switch
        {
            SyncServiceEnum.NhanhVN => await _nhanhHelper.GetNhanhConfig(shopId),
            SyncServiceEnum.Sapo => await _sapoHelper.GetSapoConfig(shopId),
            SyncServiceEnum.KiotViet => await _kiotVietHelper.GetKiotVietConfig(shopId),
            SyncServiceEnum.Odoo => await _odooHelper.GetOdooConfig(shopId),
            _ => throw new ArgumentException("SYNC_SERVICE_NOT_SUPPORTED")
        };
    }

    public async Task<Result<bool>> DeleteConfig(SyncServiceEnum syncService, string shopId)
    {
        return syncService switch
        {
            SyncServiceEnum.NhanhVN => await _nhanhHelper.DeleteNhanhConfig(shopId),
            SyncServiceEnum.Sapo => await _sapoHelper.DeleteSapoConfig(shopId),
            SyncServiceEnum.KiotViet => await _kiotVietHelper.DeleteKiotVietConfig(shopId),
            SyncServiceEnum.Odoo => await _odooHelper.DeleteOdooConfig(shopId),
            _ => Result<bool>.Failure("SYNC_SERVICE_NOT_SUPPORTED")
        };
    }

    public async Task<Result<bool>> SyncProductFromWebhook(SyncServiceEnum syncService, object productData, string shopId)
    {
        return syncService switch
        {
            SyncServiceEnum.NhanhVN => await _nhanhHelper.SyncNhanhProductFromWebhook(productData, shopId),
            SyncServiceEnum.Sapo => await _sapoHelper.SyncSapoProductFromWebhook(productData, shopId),
            SyncServiceEnum.KiotViet => await _kiotVietHelper.SyncKiotVietProductFromWebhook(productData, shopId),
            // SyncServiceEnum.Odoo => await _odooHelper.SyncOdooProductFromWebhook(productData, shopId),
            _ => Result<bool>.Failure("SYNC_SERVICE_NOT_SUPPORTED")
        };
    }

    public async Task<Result<bool>> SyncInventoryFromWebhook(SyncServiceEnum syncService, object inventoryData, string shopId)
    {
        return syncService switch
        {
            SyncServiceEnum.NhanhVN => await _nhanhHelper.SyncNhanhInventoryFromWebhook(inventoryData, shopId),
            SyncServiceEnum.Sapo => Result<bool>.Failure("SYNC_INVENTORY_NOT_SUPPORTED_FOR_SAPO"),
            SyncServiceEnum.KiotViet => Result<bool>.Failure("SYNC_INVENTORY_NOT_SUPPORTED_FOR_KIOTVIET"),
            SyncServiceEnum.Odoo => Result<bool>.Failure("SYNC_INVENTORY_NOT_SUPPORTED_FOR_ODOO"),
            _ => Result<bool>.Failure("SYNC_SERVICE_NOT_SUPPORTED")
        };
    }

    public async Task<Result<bool>> SyncOrderFromWebhook(SyncServiceEnum syncService, object orderData, string shopId)
    {
        return syncService switch
        {
            SyncServiceEnum.NhanhVN => await _nhanhHelper.SyncNhanhOrderFromWebhook(orderData, shopId),
            SyncServiceEnum.Sapo => await _sapoHelper.SyncSapoOrderFromWebhook(orderData, shopId),
            SyncServiceEnum.KiotViet => await _kiotVietHelper.SyncKiotVietOrderFromWebhook(orderData, shopId),
            // SyncServiceEnum.Odoo => await _odooHelper.SyncOdooOrderFromWebhook(orderData, shopId),
            _ => Result<bool>.Failure("SYNC_SERVICE_NOT_SUPPORTED")
        };
    }

    public async Task<Result<bool>> SyncCustomerFromWebhook(SyncServiceEnum syncService, object customerData, string shopId)
    {
        return syncService switch
        {
            SyncServiceEnum.NhanhVN => await _nhanhHelper.SyncNhanhCustomerFromWebhook(customerData, shopId),
            SyncServiceEnum.Sapo => await _sapoHelper.SyncSapoCustomerFromWebhook(customerData, shopId),
            SyncServiceEnum.KiotViet => await _kiotVietHelper.SyncKiotVietCustomerFromWebhook(customerData, shopId),
            // SyncServiceEnum.Odoo => await _odooHelper.SyncOdooCustomerFromWebhook(customerData, shopId),
            _ => Result<bool>.Failure("SYNC_SERVICE_NOT_SUPPORTED")
        };
    }

    public async Task<Result<bool>> DeleteProductsFromWebhook(SyncServiceEnum syncService, object productIds, string shopId)
    {
        return syncService switch
        {
            SyncServiceEnum.NhanhVN => await _nhanhHelper.DeleteNhanhProductsFromWebhook(productIds, shopId),
            SyncServiceEnum.Sapo => await _sapoHelper.DeleteSapoProductsFromWebhook(productIds),
            SyncServiceEnum.KiotViet => await _kiotVietHelper.DeleteKiotVietProductsFromWebhook(productIds),
            // SyncServiceEnum.Odoo => await _odooHelper.DeleteOdooProductsFromWebhook(productIds),
            _ => Result<bool>.Failure("SYNC_SERVICE_NOT_SUPPORTED")
        };
    }

    public async Task<Result<bool>> DeleteOrderFromWebhook(SyncServiceEnum syncService, object orderData, string shopId)
    {
        return syncService switch
        {
            SyncServiceEnum.NhanhVN => await _nhanhHelper.DeleteNhanhOrderFromWebhook(orderData, shopId),
            SyncServiceEnum.Sapo => await _sapoHelper.DeleteSapoOrderFromWebhook(orderData, shopId),
            SyncServiceEnum.KiotViet => await _kiotVietHelper.DeleteKiotVietOrderFromWebhook(orderData, shopId),
            // SyncServiceEnum.Odoo => await _odooHelper.DeleteOdooOrderFromWebhook(orderData, shopId),
            _ => Result<bool>.Failure("SYNC_SERVICE_NOT_SUPPORTED")
        };
    }

    public async Task<Result<bool>> CreateOrderToExternalService(SyncServiceEnum syncService, Order order, string shopId)
    {
        return syncService switch
        {
            SyncServiceEnum.NhanhVN => await _nhanhHelper.CreateOrderToNhanh(order, shopId),
            SyncServiceEnum.Sapo => await _sapoHelper.UpdateOrderToSapo(order, shopId),
            SyncServiceEnum.KiotViet => await _kiotVietHelper.UpdateOrderToKiotViet(order, shopId),
            // SyncServiceEnum.Odoo => await _odooHelper.UpdateOrderToOdoo(order, shopId),
            _ => Result<bool>.Failure("SYNC_SERVICE_NOT_SUPPORTED")
        };
    }
    public async Task<Result<bool>> UpdateOrderToExternalService(SyncServiceEnum syncService, Order order, string shopId)
    {
        return syncService switch
        {
            SyncServiceEnum.NhanhVN => await _nhanhHelper.UpdateOrderToNhanh(order, shopId),
            SyncServiceEnum.Sapo => await _sapoHelper.UpdateOrderToSapo(order, shopId),
            SyncServiceEnum.KiotViet => await _kiotVietHelper.UpdateOrderToKiotViet(order, shopId),
            // SyncServiceEnum.Odoo => await _odooHelper.UpdateOrderToOdoo(order, shopId),
            _ => Result<bool>.Failure("SYNC_SERVICE_NOT_SUPPORTED")
        };
    }

    public async Task<Result<SyncServiceConfig>> UpdateAccessCode(UpdateAccessCodeDto dto)
    {
        return dto.SyncService switch
        {
            SyncServiceEnum.NhanhVN => await _nhanhHelper.UpdateNhanhAccessCode(dto.ShopId, dto.AccessCode),
            SyncServiceEnum.Sapo => await _sapoHelper.UpdateSapoAccesToken(dto.ShopId, dto.AccessCode),
            SyncServiceEnum.KiotViet => await _kiotVietHelper.UpdateKiotVietAccessCode(dto.ShopId, dto.AccessCode),
            // SyncServiceEnum.Odoo => await _odooHelper.UpdateOdooAccessCode(dto.ShopId, dto.AccessCode),
            _ => Result<SyncServiceConfig>.Failure("SYNC_SERVICE_NOT_SUPPORTED")
        };
    }


    public async Task<SyncServiceConfig> FindByBusinessId(int businessId)
    {
        return await _syncConfigRepository.FindByBusinessId(businessId, SyncServiceEnum.NhanhVN);
    }

    public async Task<SyncServiceConfig> GetActiveConfigByShopId(string shopId)
    {
        return await _syncConfigRepository.GetActiveConfigByShopId(shopId);
    }

    public async Task<SyncServiceConfig> FindByDoMain(string domain)
    {
        return await _syncConfigRepository.FindByAdditonal(domain, SyncServiceEnum.KiotViet);
    }

    /// <summary>
    /// Đồng bộ sản phẩm trong background
    /// </summary>
    public async Task<Result<bool>> SyncProduct(string shopId)
    {
        try
        {
            var config = await GetActiveConfigByShopId(shopId);
            if (config == null)
            {
                _log.Error($"SyncProductBackground: Không tìm thấy config cho shop {shopId}");
                return Result<bool>.Failure("SYNC_CONFIG_NOT_FOUND");
            }

            _ = SyncProductBackground(shopId, config)
                .ContinueWith(t => _log.Error($"SyncProductBackground error: {t.Exception}"), TaskContinuationOptions.OnlyOnFaulted);

            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            return Result<bool>.Failure("SYNC_PRODUCT_SYNC_ERROR", ex.Message);
        }
    }

    private async Task SyncProductBackground(string shopId, SyncServiceConfig config)
    {
        try
        {
            _log.Info($"SyncProductBackground: Bắt đầu đồng bộ sản phẩm cho shop {shopId} với service {config.SyncService}");

            var result = config.SyncService switch
            {
                SyncServiceEnum.Odoo => await _odooHelper.SyncOdooProduct(shopId),
                _ => Result<bool>.Failure("SYNC_SERVICE_NOT_SUPPORTED")
            };

            if (result.IsSuccess)
                _log.Info($"SyncProductBackground: Đồng bộ sản phẩm thành công cho shop {shopId}");
            else
                _log.Error($"SyncProductBackground: Lỗi đồng bộ sản phẩm cho shop {shopId}: {string.Join(", ", result.Errors)}");
        }
        catch (Exception ex)
        {
            _log.Error($"SyncProductBackground: Exception khi đồng bộ sản phẩm cho shop {shopId}: {ex.Message}");
        }
    }
}
