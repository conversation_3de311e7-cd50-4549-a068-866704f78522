using App.ECommerce.ProcessFlow.Interface;
using App.ECommerce.ProcessFlow.Models;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Units;
using App.ECommerce.Units.Abstractions.Entities;
using App.ECommerce.Units.Consts;
using App.ECommerce.Units.Enums;
using Microsoft.Extensions.Localization;
using log4net;
using System.Globalization;
using Newtonsoft.Json;
using System.Threading.Tasks;

namespace App.ECommerce.ProcessFlow.Implement;

public class OrderValidator : IOrderValidator
{
    private readonly ILog _log = log4net.LogManager.GetLogger(typeof(OrderValidator));
    private readonly IItemsRepository _itemsRepository;
    private readonly IVoucherRepository _voucherRepository;
    private readonly IShopRepository _shopRepository;
    private readonly ITagRepository _tagRepository;
    private readonly ITransportMethodRepository _transportMethodRepository;
    private readonly IOrderRepository _orderRepository;

    public OrderValidator(
        IItemsRepository itemsRepository,
        IVoucherRepository voucherRepository,
        IShopRepository shopRepository,
        ITagRepository tagRepository,
        ITransportMethodRepository transportMethodRepository,
        IOrderRepository orderRepository)
    {
        _itemsRepository = itemsRepository;
        _voucherRepository = voucherRepository;
        _shopRepository = shopRepository;
        _tagRepository = tagRepository;
        _transportMethodRepository = transportMethodRepository;
        _orderRepository = orderRepository;
    }

    public Result<bool> ValidateItems(string? userId, List<ItemsOrder> items, List<Items> itemsList)
    {
        if (items == null || !items.Any())
            return Result<bool>.Failure("CART_LIST_ITEMS_INVALID");

        foreach (var item in items)
        {

            var detailItem = itemsList.FirstOrDefault(x => x.ItemsId == item.ItemsId);
            if (detailItem == null)
                return Result<bool>.Failure("ORDER_ITEMS_NOTFOUND");

            detailItem.Price = item.Price;

            if (item.Quantity <= 0)
                return Result<bool>.Failure("ORDER_PRODUCT_NOT_EXITS_OR_QUANTITY_EMPTY");

            bool isCheckQuantity = false;

            if (detailItem.QuantityPurchase is not null && detailItem.QuantityPurchase > 0)
                isCheckQuantity = true;

            if (isCheckQuantity && (item.Quantity ?? 0) > (detailItem.QuantityPurchase ?? 0))
                return Result<bool>.Failure("ORDER_PRODUCT_EXCEED_MAX_PURCHASE_LIMIT", detailItem.ItemsName);

            int totalQuantity = 0;
            if (!string.IsNullOrEmpty(userId))
                totalQuantity = _orderRepository.GetTotalQuantityPurchased(item.ShopId, userId, item.ItemsId);
                
            int totalQuantityNew = totalQuantity + (item.Quantity ?? 0);

            if (isCheckQuantity && totalQuantityNew > (detailItem.QuantityPurchase ?? 0))
                return Result<bool>.Failure("ORDER_PRODUCT_EXCEED_MAX_PURCHASE_LIMIT", detailItem.ItemsName);

            if (!(detailItem.SellOver ?? false) && item.Quantity > detailItem.Quantity)
                return Result<bool>.Failure("ORDER_PRODUCT_SOLD_OUT");

            // Cập nhật thông tin sản phẩm từ database
            UpdateItemInfo(item, detailItem);
        }

        return Result<bool>.Success(true);
    }

    public Result<bool> ValidateVoucherPromotion(User user, List<string> groupNames,
        List<string> voucherIds, List<ItemsOrder> items, List<Items> itemsList,
        List<Voucher> vouchers, List<String> voucherCodes)
    {
        if (voucherIds == null || !voucherIds.Any())
            return Result<bool>.Success(true);

        // Kiểm tra trùng lặp voucher
        var duplicates = voucherIds.GroupBy(x => x).Where(g => g.Count() > 1).Select(y => y.Key).ToList();
        if (duplicates.Any())
            return Result<bool>.Failure("ORDER_PROMOTION_DULICATE");

        foreach (var voucherId in voucherIds)
        {
            var voucher = vouchers.FirstOrDefault(x => x.VoucherId == voucherId);
            if (voucher == null)
                return Result<bool>.Failure("ORDER_VOUCHER_NOTFOUND");

            // Validate loại voucher
            if (voucher.VoucherType != TypeVoucher.Promotion)
                return Result<bool>.Failure("ORDER_VOUCHER_PROMOTION_TYPE_INVALID");

            // Validate thời gian sử dụng
            if (!voucher.IsLongTerm && (voucher.StartDate > DateTimes.Now() || voucher.EndDate < DateTimes.Now()))
                return Result<bool>.Failure("ORDER_VOUCHER_PROMOTION_EXPIRED");

            // Validate số lượng còn lại
            if (voucher.Quantity <= voucher.QuantityUsed)
                return Result<bool>.Failure("ORDER_VOUCHER_QUANTITY_OUT");

            // Validate điều kiện sử dụng
            var validationResult = ValidateVoucherConditions(voucher, items, groupNames, user);
            if (!validationResult.IsSuccess)
                return validationResult;
        }
        foreach (var voucherCode in voucherCodes)
        {
            // Validate số lần sử dụng của user
            var voucherUser = _voucherRepository.GetVoucherUserByVoucherCodeAndUserId(voucherCode, user.UserId).Result;
            if (voucherUser == null || voucherUser.NumUse <= 0)
                return Result<bool>.Failure("ORDER_VOUCHER_USER_USED_OUT");
        }

        return Result<bool>.Success(true);
    }

    public Result<bool> ValidateVoucherTransport(User user, List<string> groupNames,
        List<string> voucherIds, List<ItemsOrder> items, List<Items> itemsList,
        List<Voucher> vouchers, List<String> voucherCodes)
    {
        if (voucherIds == null || !voucherIds.Any())
            return Result<bool>.Success(true);

        // Kiểm tra trùng lặp voucher
        var duplicates = voucherIds.GroupBy(x => x).Where(g => g.Count() > 1).Select(y => y.Key).ToList();
        if (duplicates.Any())
            return Result<bool>.Failure("ORDER_VOUCHER_TRANSPORT_NAME");

        foreach (var voucherId in voucherIds)
        {
            var voucher = vouchers.FirstOrDefault(x => x.VoucherId == voucherId);
            if (voucher == null)
                return Result<bool>.Failure("ORDER_VOUCHER_NOTFOUND");

            // Validate loại voucher
            if (voucher.VoucherType != TypeVoucher.Transport)
                return Result<bool>.Failure("ORDER_VOUCHER_TRANSPORT_TYPE_INVALID");

            // Validate thời gian sử dụng
            if (!voucher.IsLongTerm && (voucher.StartDate > DateTimes.Now() || voucher.EndDate < DateTimes.Now()))
                return Result<bool>.Failure("ORDER_VOUCHER_TRANSPORT_EXPIRED");

            // Validate số lượng còn lại
            if (voucher.Quantity <= voucher.QuantityUsed)
                return Result<bool>.Failure("ORDER_VOUCHER_QUANTITY_OUT");

            // Validate điều kiện sử dụng
            var validationResult = ValidateVoucherConditions(voucher, items, groupNames, user);
            if (!validationResult.IsSuccess)
                return validationResult;
        }
        foreach (var voucherCode in voucherCodes)
        {
            // Validate số lần sử dụng của user
            var voucherUser = _voucherRepository.GetVoucherUserByVoucherCodeAndUserId(voucherCode, user.UserId).Result;
            if (voucherUser == null || voucherUser.NumUse <= 0)
                return Result<bool>.Failure("ORDER_VOUCHER_USER_USED_OUT");
        }

        return Result<bool>.Success(true);
    }

    public Result<Shop> ValidateShop(string shopId, string partnerId)
    {
        var shop = _shopRepository.FindByShopId(shopId);
        if (shop == null)
            return Result<Shop>.Failure("SHOP_NOT_FOUND");

        if (!string.IsNullOrEmpty(partnerId))
        {
            if (shop.PartnerId != partnerId)
                return Result<Shop>.Failure("SHOP_NOT_YOURS");
        }

        return Result<Shop>.Success(shop);
    }

    private void UpdateItemInfo(ItemsOrder item, Items detailItem)
    {
        item.ItemsCode = detailItem.ItemsCode;
        item.PartnerId = detailItem.PartnerId;
        item.ShopId = detailItem.ShopId;
        item.ItemsType = detailItem.ItemsType;
        item.CategoryIds = detailItem.CategoryIds;
        item.ItemsName = detailItem.ItemsName;
        item.ItemsNameOrigin = detailItem.ItemsNameOrigin;
        item.ItemsInfo = detailItem.ItemsInfo;
        item.Images = detailItem.Images;
        item.Sold = detailItem.Sold;
        item.IsVariant = detailItem.IsVariant;
        item.VariantImage = detailItem.VariantImage;
        item.VariantNameOne = detailItem.VariantNameOne;
        item.VariantValueOne = detailItem.VariantValueOne;
        item.VariantNameTwo = detailItem.VariantNameTwo;
        item.VariantValueTwo = detailItem.VariantValueTwo;
        item.VariantNameThree = detailItem.VariantNameThree;
        item.VariantValueThree = detailItem.VariantValueThree;
        item.PriceCapital = detailItem.PriceCapital ?? 0; // Giá vốn của sản phẩm
        item.PriceReal = detailItem.PriceReal ?? 0; // Giá gốc của sản phẩm
        item.Price = detailItem.Price ?? 0; //Giá thực tế sản phẩm
        //item.Quantity = detailItem.Quantity ?? 0; //Số lượng tồn kho của sản phẩm or số lượng mua của đơn hàng
        item.QuantityPurchase = detailItem.QuantityPurchase ?? 0; //Số lượng đã bán của sản phẩm
        item.SellOver = detailItem.SellOver ?? false; //Bán vượt số lượng quy định
        item.WarehouseId = detailItem.WarehouseId;
        item.ItemsWeight = detailItem.ItemsWeight;
        item.ItemsLength = detailItem.ItemsLength;
        item.ItemsHeight = detailItem.ItemsHeight;
    }

    private Result<bool> ValidateVoucherConditions(Voucher voucher, List<ItemsOrder> items,
        List<string> groupNames, User user)
    {
        // Validate điều kiện giới hạn (LimitType)
        switch (voucher.LimitType)
        {
            case TypeLimit.All:
                break;
            case TypeLimit.Category:
                if (voucher.CategoryIds == null || !voucher.CategoryIds.Any())
                    return Result<bool>.Failure("ORDER_VOUCHER_CATEGORY_INVALID");

                // Kiểm tra xem có sản phẩm nào thuộc các danh mục được chọn không
                bool hasValidCategory = items.Any(item =>
                    item.CategoryIds != null &&
                    item.CategoryIds.Any(categoryId => voucher.CategoryIds.Contains(categoryId)));

                if (!hasValidCategory)
                    return Result<bool>.Failure("ORDER_VOUCHER_CATEGORY_INVALID");
                break;
            case TypeLimit.Product:
                if (!items.Any(x => voucher.ProductIds.Contains(x.ItemsId)))
                    return Result<bool>.Failure("ORDER_VOUCHER_ITEMS_INVALID");
                break;
                // case TypeLimit.MinMoneyRequired:
                //     if (voucher.MinOrder > 0 && items.Sum(x => x.Price * x.Quantity) < voucher.MinOrder)
                //         return Result<bool>.Failure($"{"VOUCHER_MINIMUM_PURCHASE_REQUIRED"]} {voucher.MinOrder?.ToString("#,###", CultureInfo.GetCultureInfo("vi-VN"))} VNĐ");
                //     break;
        }

        // Validate điều kiện khách hàng (ConditionType)
        switch (voucher.ConditionType)
        {
            case TypeCondition.All:
                break;
            case TypeCondition.Group:
                if (string.IsNullOrEmpty(voucher.UserGroupId))
                    return Result<bool>.Failure("ORDER_VOUCHER_GROUP_INVALID");

                if (!groupNames.Contains(voucher.UserGroupId))
                    return Result<bool>.Failure("ORDER_VOUCHER_GROUP_INVALID");
                break;
            case TypeCondition.Customer:
                if (voucher.UserIds == null || !voucher.UserIds.Contains(user.UserId))
                    return Result<bool>.Failure("ORDER_VOUCHER_CUSTOMER_INVALID");
                break;
        }

        return Result<bool>.Success(true);
    }

    public Result<TypeTransportService> ValidateTransportService(Cart cart)
    {
        if (cart.StatusDelivery == TypeDelivery.InShop)
            return Result<TypeTransportService>.Success(TypeTransportService.LCOD);

        var shopId = cart.ShopId;
        var transportMethods = _transportMethodRepository.GetByShopId(shopId);

        // Kiểm tra xem có sản phẩm nào có TransportType là Standard
        bool hasStandardTransportItems = false;
        if (cart.ListItems?.Any() == true)
        {
            var itemIds = cart.ListItems.Select(x => x.ItemsId).Distinct().ToList();
            if (itemIds.Any())
            {
                var items = _itemsRepository.FindByItemsIds(string.Join(",", itemIds));
                hasStandardTransportItems = items.Any(item =>
                    item.TransportType?.Contains(TransportServiceType.Standard) == true);
            }
        }

        // Xác định loại dịch vụ vận chuyển
        var serviceType = hasStandardTransportItems
            ? TransportServiceType.Standard
            : TransportServiceType.Express;

        // Tìm transport method phù hợp
        var matchedMethod = transportMethods
            .FirstOrDefault(tm =>
                tm.IsEnabled &&
                tm.ServiceOptions.Any(opt =>
                    opt.ServiceType == serviceType &&
                    opt.IsEnabled));

        if (matchedMethod == null || cart.AddressId == null)
            return Result<TypeTransportService>.Success(TypeTransportService.LCOD);

        // Xác định loại transport service
        var transportService = matchedMethod.TransportCode switch
        {
            TransportCodeType.Ahamove => TypeTransportService.AHAMOVE,
            TransportCodeType.JTExpress => TypeTransportService.JTEXPRESS,
            _ => TypeTransportService.LCOD
        };

        return Result<TypeTransportService>.Success(transportService);
    }

    public Result<bool> ValidateOrderCalculation(Cart cart, OrderCalculationModel calculation)
    {
        // Kiểm tra tổng tiền hàng
        if (Math.Abs(cart.TotalAfterTax - calculation.TotalAfterTax) != 0)
        {
            _log.Info($"Tổng tiền thanh toán giỏ hàng: {cart.TotalAfterTax}");
            _log.Info($"Tổng tiền thanh toán đơn hàng: {calculation.TotalAfterTax}");
            _log.Error($"Tổng tiền thanh toán không chính xác hoặc giá sản phẩm đã thay đổi");
            return Result<bool>.Failure("ORDER_AMOUNT_NOT_MATH");
        }

        // Kiểm tra voucher khuyến mãi
        if (Math.Abs(cart.VoucherPromotionPrice - calculation.TotalPromotionDiscount) != 0)
        {
            _log.Info($"Tổng tiền voucher khuyến mãi giỏ hàng: {cart.VoucherPromotionPrice}");
            _log.Info($"Tổng tiền voucher khuyến mãi đơn hàng: {calculation.TotalPromotionDiscount}");
            _log.Error($"Tổng tiền voucher khuyến mãi không chính xác hoặc đã thay đổi");
            return Result<bool>.Failure("ORDER_VOUCHER_PROMOTION_NOT_MATH");
        }

        // Kiểm tra voucher vận chuyển
        if (Math.Abs(cart.VoucherTransportPrice - calculation.TotalShippingDiscount) != 0)
        {
            _log.Info($"Tổng tiền voucher vận chuyển giỏ hàng: {cart.VoucherTransportPrice}");
            _log.Info($"Tổng tiền voucher vận chuyển đơn hàng: {calculation.TotalShippingDiscount}");
            _log.Error($"Tổng tiền voucher vận chuyển không chính xác hoặc đã thay đổi");
            return Result<bool>.Failure("ORDER_VOUCHER_TRANSPORT_NOT_MATH");
        }

        // Kiểm tra phí vận chuyển
        long totalShippingFee = (long)(calculation.TotalShippingFee);
        if (Math.Abs(cart.TransportPrice - totalShippingFee) != 0)
        {
            _log.Info($"Tổng tiền phí vận chuyển giỏ hàng: {cart.TransportPrice}");
            _log.Info($"Tổng tiền phí vận chuyển đơn hàng: {totalShippingFee}");
            _log.Error($"Tổng tiền phí vận chuyển không chính xác hoặc phí đã thay đổi");
            return Result<bool>.Failure("ORDER_TRANSPORT_NOT_MATH");
        }

        // Kiểm tra điểm thành viên
        if (Math.Abs(cart.PointPrice - calculation.TotalPointDiscount) != 0 ||
            (cart.ExchangePoints - calculation.TotalPoints) != 0)
        {
            _log.Info($"Cart PointPrice: {cart.PointPrice}");
            _log.Info($"Order TotalPointDiscount: {calculation.TotalPointDiscount}");
            _log.Info($"Cart ExchangePoints: {cart.ExchangePoints}");
            _log.Info($"Order TotalPoints: {calculation.TotalPoints}");
            _log.Error($"Tổng tiền giảm giá bằng đổi điểm đã thay đổi");
            return Result<bool>.Failure("ORDER_AMOUNT_NOT_MATH");
        }

        // Kiểm tra giới hạn thanh toán
        if ((cart.TypePay == TypePayment.Momo || cart.TypePay == TypePayment.Zalo) &&
            (cart.Price < 1000 || cart.Price > 50000000))
        {
            _log.Error($"Yêu cầu bị từ chối vì số tiền giao dịch nhỏ hơn 1,000 VND hoặc lớn hơn 50,000,000 VND");
            return Result<bool>.Failure("ORDER_AMOUNT_REJECT");
        }

        return Result<bool>.Success(true);
    }
}