using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using App.ECommerce.Repository.Entities;
using App.ECommerce.Resource.Dtos;
using App.ECommerce.Resource.Dtos.OdooDtos;
using App.ECommerce.Units.Abstractions.Entities;

namespace App.ECommerce.Helpers.Interface;

public interface IOdooHelper
{
    Task<Result<SyncServiceConfig>> SaveOdooConfig(SyncServiceConfigDto dto);
    Task<SyncServiceConfig> GetOdooConfig(string shopId);
    Task<Result<bool>> DeleteOdooConfig(string shopId);
    Task<Result<bool>> SyncOdooProduct(string shopId);
    Task<Result<OdooStoresResponseDto>> SyncOdooStores(string shopId);
    // // Task<Result<bool>> SyncOdooProductFromWebhook(object productData, string shopId);
    // Task<Result<bool>> SyncOdooOrderFromWebhook(object orderData, string shopId);
    // Task<Result<bool>> SyncOdooCustomerFromWebhook(object customerData, string shopId);
    // Task<Result<bool>> DeleteOdooProductsFromWebhook(object productIds);
    // Task<Result<bool>> DeleteOdooOrderFromWebhook(object orderData, string shopId);
    // Task<Result<bool>> UpdateOrderToOdoo(Order order, string shopId);
}