using App.ECommerce.Helpers.Interface;
using App.ECommerce.ProcessFlow.Interface;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Resource.Dtos;
using App.ECommerce.Resource.Dtos.SapoOmniDtos;
using App.ECommerce.Units.Abstractions.Entities;
using App.ECommerce.Units.Enums;
using App.ECommerce.Repository.Interface;
using App.Base.Repository.Entities;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Threading.Tasks;
using AutoMapper;
using System.Text;
using System.Net.Http;
using App.ECommerce.Services.UploadStore;
using System.Text.Json.Nodes;
using App.ECommerce.Repository.Implement;

namespace App.ECommerce.Helpers;

public class SapoHelper : ISapoHelper
{
    private readonly ILogger<SapoHelper> _logger;
    private readonly IItemsRepository _itemsRepository;
    private readonly IOrderRepository _orderRepository;
    private readonly ICategoryRepository _categoryRepository;
    private readonly IUserRepository _userRepository;
    private readonly ISyncServiceConfigRepository _syncConfigRepository;
    private readonly IMapper _mapper;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IConfiguration _configuration;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IShopRepository _shopRepository;

    public SapoHelper(
        ILogger<SapoHelper> logger,
        IItemsRepository itemsRepository,
        IOrderRepository orderRepository,
        ICategoryRepository categoryRepository,
        IUserRepository userRepository,
        ISyncServiceConfigRepository syncConfigRepository,
        IMapper mapper,
        IHttpClientFactory httpClientFactory,
        IConfiguration configuration,
        IHttpContextAccessor httpContextAccessor,
        IShopRepository shopRepository
        )
    {
        _logger = logger;
        _itemsRepository = itemsRepository;
        _orderRepository = orderRepository;
        _categoryRepository = categoryRepository;
        _userRepository = userRepository;
        _syncConfigRepository = syncConfigRepository;
        _mapper = mapper;
        _httpClientFactory = httpClientFactory;
        _configuration = configuration;
        _httpContextAccessor = httpContextAccessor;
        _shopRepository = shopRepository;
    }

    public async Task<Result<SyncServiceConfig>> SaveSapoConfig(SyncServiceConfigDto dto)
    {
        try
        {
            var existingConfig = await _syncConfigRepository.FindByShopIdAndService(dto.ShopId, SyncServiceEnum.Sapo);

            var config = new SyncServiceConfig
            {
                ShopId = dto.ShopId,
                SyncService = SyncServiceEnum.Sapo,
                AdditionalConfig = dto.DomainApi,
                AccessToken = dto.AccessToken,
                AppId = dto.ClientId,
                SecretKey = dto.ClientSecret,
                Status = TypeStatus.Actived,
                CreatedDate = existingConfig?.CreatedDate ?? DateTime.UtcNow,
                ModifiedDate = DateTime.UtcNow
            };

            SyncServiceConfig savedConfig;
            if (existingConfig != null)
            {
                config.Id = existingConfig.Id;
                savedConfig = await _syncConfigRepository.CreateOrUpdate(config);
            }
            else
            {
                savedConfig = await _syncConfigRepository.CreateOrUpdate(config);
            }

            // Tạo webhook bằng Basic Auth (ClientId:ClientSecret)
            if (!string.IsNullOrEmpty(dto.ClientId) && !string.IsNullOrEmpty(dto.ClientSecret) && !string.IsNullOrEmpty(dto.DomainApi))
            {
                var webhookUrl = GetDefaultWebhookUrl();
                var webhookResult = await CreateWebhooksAsync(dto.ShopId, webhookUrl);
                if (!webhookResult.IsSuccess)
                {
                    _logger.LogWarning("Failed to create webhooks for shop {ShopId}: {Error}",
                        dto.ShopId, webhookResult.Message);
                    // Không fail toàn bộ process nếu webhook thất bại
                }
                else
                {
                    _logger.LogInformation("Successfully created webhooks for shop {ShopId}", dto.ShopId);
                }
            }
            else
            {
                _logger.LogWarning("Missing ClientId, ClientSecret or DomainApi for shop {ShopId}, skipping webhook creation", dto.ShopId);
            }

            return Result<SyncServiceConfig>.Success(savedConfig);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving Sapo config for shop {ShopId}", dto.ShopId);
            return Result<SyncServiceConfig>.Failure($"Failed to save Sapo config: {ex.Message}");
        }
    }

    public async Task<SyncServiceConfig> GetSapoConfig(string shopId)
    {
        try
        {
            var config = await _syncConfigRepository.FindByShopIdAndService(shopId, SyncServiceEnum.Sapo);
            return config;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting Sapo config for shop {ShopId}", shopId);
            return null;
        }
    }

    public async Task<Result<bool>> DeleteSapoConfig(string shopId)
    {
        try
        {
            var config = await _syncConfigRepository.FindByShopIdAndService(shopId, SyncServiceEnum.Sapo);
            if (config == null)
            {
                return Result<bool>.Failure("Config not found");
            }

            await _syncConfigRepository.DeleteByShopIdAndService(shopId, SyncServiceEnum.Sapo);
            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting Sapo config for shop {ShopId}", shopId);
            return Result<bool>.Failure($"Failed to delete Sapo config: {ex.Message}");
        }
    }

    public async Task<Result<SyncServiceConfig>> UpdateSapoAccesToken(string shopId, string accessToken)
    {
        try
        {
            if (string.IsNullOrEmpty(accessToken))
            {
                return Result<SyncServiceConfig>.Failure("Access code is required");
            }

            var config = await _syncConfigRepository.FindByShopIdAndService(shopId, SyncServiceEnum.Sapo);
            if (config == null)
            {
                return Result<SyncServiceConfig>.Failure("Config not found");
            }

            var tokenResult = await ExchangeCodeForToken(config.AdditionalConfig, accessToken, config.AppId, config.SecretKey);
            if (!tokenResult.IsSuccess)
            {
                return Result<SyncServiceConfig>.Failure(tokenResult.Message);
            }

            config.AccessToken = tokenResult.Data;
            config.ModifiedDate = DateTime.UtcNow;
            config.Status = TypeStatus.Actived;

            var updatedConfig = await _syncConfigRepository.CreateOrUpdate(config);

            // Tạo webhook bằng Basic Auth sau khi có access token
            var webhookUrl = GetDefaultWebhookUrl();
            var webhookResult = await CreateWebhooksAsync(shopId, webhookUrl);
            if (!webhookResult.IsSuccess)
            {
                _logger.LogWarning("Failed to create webhooks for shop {ShopId}: {Error}",
                    shopId, webhookResult.Message);
            }
            else
            {
                _logger.LogInformation("Successfully created webhooks for shop {ShopId}", shopId);
            }

            return Result<SyncServiceConfig>.Success(updatedConfig);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating Sapo access code for shop {ShopId}", shopId);
            return Result<SyncServiceConfig>.Failure($"Failed to update access code: {ex.Message}");
        }
    }

    private async Task<Result<string>> ExchangeCodeForToken(string domainApi, string code, string clientId, string clientSecret)
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient();

            var tokenUrl = $"https://{domainApi}/admin/oauth/access_token";
            var requestData = new
            {
                grant_type = "authorization_code",
                client_id = clientId,
                client_secret = clientSecret,
                code = code
            };

            var json = JsonConvert.SerializeObject(requestData);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await httpClient.PostAsync(tokenUrl, content);
            var responseContent = await response.Content.ReadAsStringAsync();

            if (response.IsSuccessStatusCode)
            {
                var tokenResponse = JsonConvert.DeserializeObject<dynamic>(responseContent);
                var accessToken = tokenResponse?.access_token?.ToString();

                if (!string.IsNullOrEmpty(accessToken))
                {
                    return Result<string>.Success(accessToken);
                }

                return Result<string>.Failure("Access token not found in response");
            }

            return Result<string>.Failure($"Token exchange failed: {responseContent}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exchanging code for token");
            return Result<string>.Failure($"Token exchange error: {ex.Message}");
        }
    }

    public async Task<Result<bool>> SyncSapoProductFromWebhook(object productData, string shopId)
    {
        try
        {
            var productDto = JsonConvert.DeserializeObject<SapoOmniProductDto>(productData.ToString());
            if (productDto == null)
            {
                return Result<bool>.Failure("Invalid product data");
            }

            await ProcessProduct(productDto, shopId);
            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing Sapo product webhook for shop {ShopId}", shopId);
            return Result<bool>.Failure($"Product sync error: {ex.Message}");
        }
    }

    private async Task ProcessProduct(SapoOmniProductDto product, string shopId)
    {
        try
        {
            // Get all categories for this product
            var categoryIds = await EnsureCategoryExists(product, shopId);

            if (categoryIds.Count == 0)
            {
                _logger.LogWarning("No categories found for product {ProductId}, skipping", product.Id);
                return;
            }

            var existingItems = _itemsRepository.FindByExternalIds(product.Id.ToString(), SyncServiceEnum.Sapo);

            // Check if this is a variant product
            bool isVariant = product.Variants != null &&
                            (product.Variants.Count > 1 ||
                             product.Variants.Any(v => !string.IsNullOrEmpty(v.Option1) && v.Option1 != "Default Title"));

            // Delete existing items
            if (existingItems != null)
            {
                foreach (var item in existingItems)
                {
                    _itemsRepository.DeleteItems(item.ItemsId);
                }
            }

            if (!isVariant)
            {
                // Single product without variants
                var firstVariant = product.Variants?.FirstOrDefault();
                var newItem = MapSapoProductToItems(product, firstVariant, shopId, categoryIds, false);
                _itemsRepository.CreateItems(newItem);
            }
            else
            {
                // Product with variants
                foreach (var variant in product.Variants)
                {
                    var newItem = MapSapoProductToItems(product, variant, shopId, categoryIds, true);
                    _itemsRepository.CreateItems(newItem);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing product {ProductId}", product.Id);
            throw;
        }
    }

    private Items MapSapoProductToItems(SapoOmniProductDto sapoProduct, SapoOmniVariantDto? variant, string shopId, List<string> categoryIds, bool isVariant)
    {
        var item = new Items
        {
            ItemsName = sapoProduct.Name,
            ItemsCode = sapoProduct.Id.ToString(),
            ExternalSource = SyncServiceEnum.Sapo,
            CategoryIds = categoryIds, // Use the list of category IDs
            ItemsInfo = sapoProduct.Description ?? "",
            ItemsType = TypeItems.Product,
            ShopId = shopId,
            ExternalId = sapoProduct.Id.ToString(),
            IsTop = false,
            IsShow = sapoProduct.Status == "active",
            IsVariant = isVariant,
            TypePublish = sapoProduct.Status == "active" ? TypePublish.Publish : TypePublish.UnPublish,
            Status = TypeStatus.Actived,
            Created = sapoProduct.CreatedOn,
            Updated = sapoProduct.ModifiedOn,
            SeoTags = MapSeoTags(sapoProduct.Tags),
            PartnerId = "",
            WarehouseId = "",
            Images = GetItemImages(sapoProduct, variant),
        };

        if (variant != null)
        {
            item.PriceReal = (long)(variant.CompareAtPrice > 0 ? variant.CompareAtPrice : variant.Price);
            item.Price = (long)variant.Price;
            item.Quantity = variant.InventoryQuantity;
            item.ItemsWeight = (double)variant.Weight;
            item.ItemsCode = sapoProduct.Id.ToString();
            item.CustomTaxRate = variant.Taxable ? 10 : 0;
        }

        if (isVariant && variant != null)
        {
            MapVariantOptions(sapoProduct, variant, item);
        }

        return item;
    }
    private List<SeoTag> MapSeoTags(string? tags)
    {
        var seoTags = new List<SeoTag>();

        if (!string.IsNullOrEmpty(tags))
        {
            var tagArray = tags.Split(',', StringSplitOptions.RemoveEmptyEntries);
            foreach (var tag in tagArray)
            {
                seoTags.Add(new SeoTag
                {
                    Tags = tag.Trim(),
                });
            }
        }

        return seoTags;
    }

    private string GetDisplayName(SapoOmniProductDto product, SapoOmniVariantDto? variant, bool isVariant)
    {
        if (!isVariant || variant == null)
        {
            return product.Name;
        }

        // If variant has a meaningful title (not "Default Title"), use it
        if (!string.IsNullOrEmpty(variant.Title) && variant.Title != "Default Title")
        {
            return $"{product.Name} - {variant.Title}";
        }

        // Otherwise, construct from options
        var options = new List<string>();
        if (!string.IsNullOrEmpty(variant.Option1) && variant.Option1 != "Default Title")
            options.Add(variant.Option1);
        if (!string.IsNullOrEmpty(variant.Option2))
            options.Add(variant.Option2);
        if (!string.IsNullOrEmpty(variant.Option3))
            options.Add(variant.Option3);

        if (options.Count > 0)
        {
            return $"{product.Name} - {string.Join(" / ", options)}";
        }

        return product.Name;
    }

    private void MapVariantOptions(SapoOmniProductDto product, SapoOmniVariantDto variant, Items item)
    {
        var options = product.Options.OrderBy(o => o.Position).ToList();

        if (options.Count >= 1 && !string.IsNullOrEmpty(variant.Option1))
        {
            item.VariantNameOne = options[0].Name;
            item.VariantValueOne = variant.Option1;
        }

        if (options.Count >= 2 && !string.IsNullOrEmpty(variant.Option2))
        {
            item.VariantNameTwo = options[1].Name;
            item.VariantValueTwo = variant.Option2;
        }

        if (options.Count >= 3 && !string.IsNullOrEmpty(variant.Option3))
        {
            item.VariantNameThree = options[2].Name;
            item.VariantValueThree = variant.Option3;
        }
    }

    private List<MediaInfo> GetItemImages(SapoOmniProductDto product, SapoOmniVariantDto? variant)
    {
        var images = new List<MediaInfo>();

        // If variant has specific image, prioritize it
        if (variant?.ImageId.HasValue == true)
        {
            var variantImage = product.Images?.FirstOrDefault(img => img.Id == variant.ImageId.Value);
            if (variantImage != null)
            {
                images.Add(new MediaInfo
                {
                    Link = variantImage.Src,
                    Type = TypeMedia.IMAGE
                });
            }
        }

        // Add all product images (excluding variant-specific image if already added)
        var productImages = product.Images?
            .Where(img => variant?.ImageId == null || img.Id != variant.ImageId.Value)
            .OrderBy(img => img.Position)
            .Select(img => new MediaInfo
            {
                Link = img.Src,
                Type = TypeMedia.IMAGE
            })
            .ToList() ?? new List<MediaInfo>();

        images.AddRange(productImages);

        // If no images found, use the main product image
        if (images.Count == 0 && product.Image != null)
        {
            images.Add(new MediaInfo
            {
                Link = product.Image.Src,
                Type = TypeMedia.IMAGE
            });
        }

        return images;
    }

    public async Task<Result<bool>> SyncSapoOrderFromWebhook(object orderData, string shopId)
    {
        try
        {
            _logger.LogInformation("Processing Sapo order webhook for shop {ShopId}", shopId);

            // Handle both direct order object and wrapped order object
            SapoOmniOrderDto orderDto = null;

            var jsonString = orderData.ToString();


            orderDto = JsonConvert.DeserializeObject<SapoOmniOrderDto>(jsonString);


            if (orderDto == null)
            {
                return Result<bool>.Failure("Invalid order data");
            }

            _logger.LogInformation("Processing order {OrderId} ({OrderName}) from Sapo webhook",
                orderDto.Id, orderDto.Name);

            await ProcessOrder(orderDto, shopId);
            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing Sapo order webhook for shop {ShopId}", shopId);
            return Result<bool>.Failure($"Order sync error: {ex.Message}");
        }
    }
    public async Task<Result<bool>> SyncSapoCustomerFromWebhook(object customerData, string shopId)
    {
        try
        {
            var customerDto = JsonConvert.DeserializeObject<SapoOmniCustomerDto>(customerData.ToString());
            if (customerDto == null)
            {
                return Result<bool>.Failure("Invalid customer data");
            }

            await ProcessCustomer(customerDto, shopId);
            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing Sapo customer webhook for shop {ShopId}", shopId);
            return Result<bool>.Failure($"Customer sync error: {ex.Message}");
        }
    }

    public async Task<Result<bool>> DeleteSapoProductsFromWebhook(object productId)
    {
        try
        {

            var products = _itemsRepository.FindByExternalIds((string)productId, SyncServiceEnum.Sapo);
            if (products != null)
            {
                foreach (var product in products)
                {
                    _itemsRepository.DeleteItems(product.ItemsId);
                }
            }


            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting Sapo products from webhook");
            return Result<bool>.Failure($"Product deletion error: {ex.Message}");
        }
    }

    public async Task<Result<bool>> DeleteSapoOrderFromWebhook(object orderData, string shopId)
    {
        try
        {
            string orderId = null;
            if (orderData is string stringId)
            {
                orderId = stringId;
            }
            else
            {
                var orderDto = JsonConvert.DeserializeObject<dynamic>(orderData.ToString());
                orderId = orderDto?.Id?.ToString() ?? orderDto?.id?.ToString();
            }

            if (string.IsNullOrEmpty(orderId))
            {
                return Result<bool>.Failure("Invalid order ID");
            }

            var order = _orderRepository.FindByExternalId(shopId, orderId, SyncServiceEnum.Sapo);
            if (order != null)
            {
                _orderRepository.DeleteOrder(order.Id);
            }

            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting Sapo order from webhook for shop {ShopId}", shopId);
            return Result<bool>.Failure($"Order deletion error: {ex.Message}");
        }
    }
    private async Task ProcessOrder(SapoOmniOrderDto order, string shopId)
    {
        try
        {
            Shop? shop = _shopRepository.FindByShopId(shopId);
            _logger.LogInformation("Processing order {OrderId} ({OrderName}) for shop {ShopId}",
                order.Id, order.Name, shopId);

            // Process customer if available
            User? user = null;
            if (order.Customer != null)
            {
                user = await EnsureUserFromSapoOmniCustomer(order.Customer, shopId);
            }

            // Check if order already exists
            var existingOrder = _orderRepository.FindByExternalId(shopId, order.Id.ToString(), SyncServiceEnum.Sapo);
            if (existingOrder != null && existingOrder.ShopId != shopId)
            {
                existingOrder = null;
            }

            // Map line items
            var listItems = await MapOrderLineItems(shop, order.LineItems);

            // Map shipping address
            var shippingAddress = MapShippingAddress(user);

            if (existingOrder != null)
            {
                // Update existing order
                await UpdateExistingOrder(existingOrder, order, listItems, shippingAddress, user);
                _logger.LogInformation("Updated existing order {OrderId}", existingOrder.OrderId);
            }
            else
            {
                // Create new order
                await CreateNewOrder(order, shop, listItems, shippingAddress, user);
                _logger.LogInformation("Created new order for Sapo order {SapoOrderId}", order.Id);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing order {OrderId}", order.Id);
            throw;
        }
    }

    private async Task<List<ItemsOrder>> MapOrderLineItems(Shop shop, List<SapoOmniLineItemDto> lineItems)
    {
        var listItems = new List<ItemsOrder>();

        foreach (var lineItem in lineItems)
        {
            try
            {
                // Try to find existing item by variant ID first, then by product ID
                Items? existingItem = null;

                if (lineItem.VariantId > 0)
                {
                    existingItem = _itemsRepository.FindByExternalId(shop.Id, lineItem.VariantId.ToString(), SyncServiceEnum.Sapo);
                }

                if (existingItem == null && lineItem.ProductId > 0)
                {
                    var productItems = _itemsRepository.FindByExternalIds(lineItem.ProductId.ToString(), SyncServiceEnum.Sapo);
                    existingItem = productItems?.FirstOrDefault();
                }

                var itemOrder = new ItemsOrder
                {
                    ItemsCode = lineItem.Sku ?? lineItem.Id.ToString(),
                    ItemsName = existingItem?.ItemsName ?? lineItem.Name,
                    ItemsId = existingItem?.ItemsId,
                    Quantity = lineItem.Quantity,
                    Price = (long)lineItem.Price,
                    Note = lineItem.Note ?? "",
                    Images = existingItem.Images,
                    VoucherDiscount = (long)lineItem.TotalDiscount,
                    TaxAmount = 0, // Tax amount not available in this structure
                    ExternalId = lineItem.VariantId.ToString(),
                    ExternalSource = SyncServiceEnum.Sapo,
                    VariantValueOne = GetVariantOption(lineItem.VariantTitle, 1),
                    VariantValueTwo = GetVariantOption(lineItem.VariantTitle, 2),
                    VariantValueThree = GetVariantOption(lineItem.VariantTitle, 3)
                };

                listItems.Add(itemOrder);

                _logger.LogDebug("Mapped line item {LineItemId}: {ItemName} x{Quantity}",
                    lineItem.Id, lineItem.Name, lineItem.Quantity);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error mapping line item {LineItemId}", lineItem.Id);
            }
        }

        return listItems;
    }

    private string? GetVariantOption(string? variantTitle, int optionIndex)
    {
        if (string.IsNullOrEmpty(variantTitle) || variantTitle == "Default Title")
            return null;

        var options = variantTitle.Split(" / ", StringSplitOptions.RemoveEmptyEntries);
        return optionIndex <= options.Length ? options[optionIndex - 1] : null;
    }

    private ShippingAddress MapShippingAddress(User user)
    {

        if (user == null) return null;
        return new ShippingAddress
        {
            Id = user.Id,
            FullName = user.Fullname,
            UserId = user.UserId,
            PhoneNumber = user.PhoneNumber,
            WardName = user.WardName,
            DistrictName = user.DistrictName,
            ProvinceName = user.ProvinceName,

        };
    }

    private async Task UpdateExistingOrder(Order existingOrder, SapoOmniOrderDto order, List<ItemsOrder> listItems, ShippingAddress shippingAddress, User? user)
    {
        existingOrder.ListItems = listItems;
        existingOrder.Price = (long)order.TotalPrice;
        existingOrder.VoucherPromotionPrice = (long)order.TotalDiscounts;
        existingOrder.TotalTaxAmount = (long)order.TotalTax;
        existingOrder.TotalAfterTax = (long)order.OriginalTotalPrice;
        existingOrder.Notes = order.Note ?? "";
        existingOrder.UserShippingAddress = shippingAddress;
        existingOrder.TransportPrice = (long)order.TotalShippingPrice;
        existingOrder.Updated = order.ModifiedOn;
        existingOrder.Creator = shippingAddress;
        // Map statuses
        MapSapoStatusesToInternalOrder(order, existingOrder);

        _orderRepository.UpdateOrder(existingOrder);
    }

    private async Task CreateNewOrder(SapoOmniOrderDto order, Shop shop, List<ItemsOrder> listItems, ShippingAddress shippingAddress, User? user)
    {
        var newOrder = new Order
        {
            OrderId = Guid.NewGuid().ToString(),
            OrderNo = order.Name,
            ListItems = listItems,
            Price = (long)order.TotalPrice,
            VoucherPromotionPrice = (long)order.TotalDiscounts,
            TotalTaxAmount = (long)order.TotalTax,
            TotalAfterTax = (long)order.OriginalTotalPrice,
            OrderOrigin = TypeOrigin.SapoOmni,
            UserShippingAddress = shippingAddress,
            ShopId = shop.ShopId,
            PartnerId = shop.PartnerId,
            ShopName = shop.ShopName,
            Status = TypeStatus.Actived,
            Created = order.CreatedOn,
            Updated = order.ModifiedOn,
            ExternalId = order.Id.ToString(),
            ExternalSource = SyncServiceEnum.Sapo,
            TransportPrice = (long)order.TotalShippingPrice,
            Creator = shippingAddress
        };

        // Map statuses
        MapSapoStatusesToInternalOrder(order, newOrder);

        await _orderRepository.CreateOrder(newOrder);
    }

    private void MapSapoStatusesToInternalOrder(SapoOmniOrderDto sapoOrder, Order internalOrder)
    {
        // Map financial status
        switch (sapoOrder.FinancialStatus?.ToLower())
        {
            case "paid":
                internalOrder.StatusPay = TypePayStatus.Paid;
                break;
            case "refunded":
            case "partially_refunded":
                internalOrder.StatusPay = TypePayStatus.Refund;
                break;
            case "pending":
            case "partially_paid":
            case "voided":
            default:
                internalOrder.StatusPay = TypePayStatus.NotPaid;
                break;
        }

        // Map fulfillment status
        switch (sapoOrder.FulfillmentStatus?.ToLower())
        {
            case "fulfilled":
                internalOrder.StatusTransport = TypeTransportStatus.Success;
                break;
            case "partial":
                internalOrder.StatusTransport = TypeTransportStatus.Delivering;
                break;
            case "shipped":
                internalOrder.StatusTransport = TypeTransportStatus.Transporting;
                break;
            case "cancelled":
                internalOrder.StatusTransport = TypeTransportStatus.Cancel;
                break;
            case null:
            case "":
            case "unfulfilled":
            default:
                internalOrder.StatusTransport = TypeTransportStatus.WaitingForDelivery;
                break;
        }

        // Map order status
        switch (sapoOrder.Status?.ToLower())
        {
            case "cancelled":
                internalOrder.StatusOrder = TypeOrderStatus.Failed;
                internalOrder.StatusTransport = TypeTransportStatus.Cancel;
                break;
            case "closed":
                internalOrder.StatusOrder = TypeOrderStatus.Success;
                break;
            case "draft":
                internalOrder.StatusOrder = TypeOrderStatus.Pending;
                break;
            case "open":
                if (internalOrder.StatusPay == TypePayStatus.Paid)
                {
                    internalOrder.StatusOrder = TypeOrderStatus.Paid;
                }
                else
                {
                    internalOrder.StatusOrder = TypeOrderStatus.Verified;
                }
                break;
            case "finalized":
            default:
                if (internalOrder.StatusPay == TypePayStatus.Paid)
                {
                    internalOrder.StatusOrder = TypeOrderStatus.Paid;
                }
                else
                {
                    internalOrder.StatusOrder = TypeOrderStatus.Pending;
                }
                break;
        }

        // Special case: if both payment and fulfillment are complete
        if (sapoOrder.FulfillmentStatus == "fulfilled" && sapoOrder.FinancialStatus == "paid")
        {
            internalOrder.StatusOrder = TypeOrderStatus.Success;
            internalOrder.StatusTransport = TypeTransportStatus.Success;
        }
    }

    private async Task ProcessCustomer(SapoOmniCustomerDto customer, string shopId)
    {
        try
        {
            _logger.LogInformation("Processing customer {CustomerId} ({CustomerName}) for shop {ShopId}",
                customer.Id, customer.FullName, shopId);

            // Check if customer already exists
            var existingCustomer = _userRepository.FindByUsername(shopId, customer.FullName);

            if (existingCustomer != null)
            {
                var updatedCustomer = MapSapoCustomerToUser(customer, shopId, existingCustomer.UserId);
                _logger.LogInformation("Updated existing customer {CustomerId}", existingCustomer.UserId);
            }
            else
            {
                // Create new customer
                var newCustomer = MapSapoCustomerToUser(customer, shopId);
                _userRepository.CreateUser(newCustomer);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing customer {CustomerId}", customer.Id);
            throw;
        }
    }


    private User MapSapoCustomerToUser(SapoOmniCustomerDto sapoCustomer, string shopId, string? existingUserId = null)
    {
        var user = new User
        {
            UserId = existingUserId ?? Guid.NewGuid().ToString(),
            ShopId = shopId,
            Username = sapoCustomer.FullName,
            Fullname = sapoCustomer.FullName,
            Email = sapoCustomer.Email ?? "",
            PhoneNumber = sapoCustomer.DisplayPhone,
            Status = TypeStatus.Actived,
            Created = existingUserId == null ? sapoCustomer.CreatedOn : DateTime.UtcNow,
            Updated = sapoCustomer.ModifiedOn,


            Gender = MapGender(sapoCustomer.Gender),
            Birthdate = sapoCustomer.DateOfBirth,
            Notes = sapoCustomer.Note ?? "",

        };

        if (sapoCustomer.DefaultAddress != null)
        {
            MapAddressToUser(user, sapoCustomer.DefaultAddress);
        }
        else if (sapoCustomer.Addresses != null && sapoCustomer.Addresses.Count > 0)
        {
            MapAddressToUser(user, sapoCustomer.Addresses.First());
        }

        return user;
    }

    private void MapAddressToUser(User user, SapoOmniAddressDto address)
    {
        user.Address = BuildDetailedAddress(address);
        user.WardName = address.Ward ?? "";
        user.DistrictName = address.District ?? "";
        user.ProvinceName = address.Province ?? address.City ?? "";

    }

    private string BuildDetailedAddress(SapoOmniAddressDto address)
    {
        var addressParts = new List<string>();

        if (!string.IsNullOrEmpty(address.Address1))
            addressParts.Add(address.Address1);
        if (!string.IsNullOrEmpty(address.Address2))
            addressParts.Add(address.Address2);

        return string.Join(", ", addressParts);
    }

    private TypeGender MapGender(string gender)
    {
        return gender?.ToLower() switch
        {
            "male" => TypeGender.Male,
            "female" => TypeGender.Female,
            _ => TypeGender.Other
        };
    }


    private async Task<User?> EnsureUserFromSapoOmniCustomer(SapoOmniCustomerDto customer, string shopId)
    {
        try
        {


            // Try to find by phone, email or name for backward compatibility
            var existingUser = FindExistingUser(customer.DisplayPhone, customer.Email, shopId, customer.FullName);

            if (existingUser != null)
            {
                // Update existing user with Sapo data
                var updatedUser = MapSapoCustomerToUser(customer, shopId, existingUser.UserId);
                _userRepository.UpdateUser(updatedUser);
                _logger.LogInformation("Updated existing user {UserId} with Sapo customer data", existingUser.UserId);
                return updatedUser;
            }
            else
            {
                // Create new customer
                var newCustomer = MapSapoCustomerToUser(customer, shopId);
                _userRepository.CreateUser(newCustomer);
                _logger.LogInformation("Created new customer {CustomerId} for Sapo customer {SapoId}",
                    newCustomer.UserId, customer.Id);
                return newCustomer;
            }

        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error ensuring user from SapoOmni customer {CustomerId}", customer.Id);
            return null;
        }
    }

    // Keep existing methods for backward compatibility but update to use new User entity
    private User? FindExistingUser(string phoneNumber, string email, string shopId, string name)
    {
        User? user = null;

        if (!string.IsNullOrEmpty(phoneNumber))
        {
            user = _userRepository.FindByUserPhone(shopId, phoneNumber);
        }

        if (user == null && !string.IsNullOrEmpty(email))
        {
            user = _userRepository.FindByUserEmail(shopId, email);
        }

        if (user == null && !string.IsNullOrEmpty(name))
        {
            user = _userRepository.FindByUsername(shopId, name);
        }

        return user;
    }

    private User CreateNewUser(SapoOmniCustomerDto customer, string shopId)
    {
        var user = MapSapoCustomerToUser(customer, shopId);
        _userRepository.CreateUser(user);
        return user;
    }

    private void UpdateExistingUser(User user, SapoOmniCustomerDto customer)
    {
        var updatedUser = MapSapoCustomerToUser(customer, user.ShopId, user.UserId);
        _userRepository.UpdateUser(updatedUser);
    }

    // Update CreateCustomerToSapoOmniAsync to use new User mapping
    public async Task<Result<string>> CreateCustomerToSapoOmniAsync(User customer, string shopId)
    {
        try
        {
            var config = await GetSapoConfig(shopId);
            if (config == null)
            {
                return Result<string>.Failure("Sapo config not found");
            }

            using var httpClient = _httpClientFactory.CreateClient();
            var url = $"https://{config.AdditionalConfig}/admin/customers.json";

            var basicAuth = Convert.ToBase64String(Encoding.UTF8.GetBytes($"{config.AppId}:{config.SecretKey}"));
            httpClient.DefaultRequestHeaders.Add("Authorization", $"Basic {basicAuth}");
            httpClient.DefaultRequestHeaders.Add("User-Agent", "ECommerceApp/1.0");

            var sapoCustomer = MapUserToSapoCustomerDto(customer);
            var requestBody = new { customer = sapoCustomer };

            var json = JsonConvert.SerializeObject(requestBody);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await httpClient.PostAsync(url, content);
            var responseContent = await response.Content.ReadAsStringAsync();

            if (response.IsSuccessStatusCode)
            {
                var result = JsonConvert.DeserializeObject<dynamic>(responseContent);
                var customerId = result?.customer?.id?.ToString();
                return Result<string>.Success(customerId ?? "");
            }

            return Result<string>.Failure($"Failed to create customer: {responseContent}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating customer to Sapo");
            return Result<string>.Failure($"Error creating customer: {ex.Message}");
        }
    }

    // Update UpdateCustomerToSapoOmniAsync
    public async Task<Result<bool>> UpdateCustomerToSapoOmniAsync(User customer, string shopId)
    {
        try
        {
            if (string.IsNullOrEmpty(customer.PhoneNumber))
            {
                var createResult = await CreateCustomerToSapoOmniAsync(customer, shopId);
                return createResult.IsSuccess ? Result<bool>.Success(true) : Result<bool>.Failure(createResult.Message);
            }

            var config = await GetSapoConfig(shopId);
            if (config == null)
            {
                return Result<bool>.Failure("Sapo config not found");
            }

            using var httpClient = _httpClientFactory.CreateClient();
            var url = $"https://{config.AdditionalConfig}/admin/customers/{customer.PhoneNumber}.json";

            var basicAuth = Convert.ToBase64String(Encoding.UTF8.GetBytes($"{config.AppId}:{config.SecretKey}"));
            httpClient.DefaultRequestHeaders.Add("Authorization", $"Basic {basicAuth}");
            httpClient.DefaultRequestHeaders.Add("User-Agent", "ECommerceApp/1.0");

            var sapoCustomer = MapUserToSapoCustomerDto(customer);
            var requestBody = new { customer = sapoCustomer };

            var json = JsonConvert.SerializeObject(requestBody);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await httpClient.PutAsync(url, content);

            if (response.IsSuccessStatusCode)
            {
                return Result<bool>.Success(true);
            }

            var responseContent = await response.Content.ReadAsStringAsync();
            return Result<bool>.Failure($"Failed to update customer: {responseContent}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating customer to Sapo");
            return Result<bool>.Failure($"Error updating customer: {ex.Message}");
        }
    }

    // Enhanced MapUserToSapoCustomerDto with more fields
    private object MapUserToSapoCustomerDto(User customer)
    {
        return new
        {
            first_name = customer.Fullname?.Split(' ').FirstOrDefault() ?? "",
            last_name = customer.Fullname?.Contains(' ') == true
                ? string.Join(" ", customer.Fullname.Split(' ').Skip(1))
                : "",
            name = customer.Fullname,
            email = customer.Email,
            phone = customer.PhoneNumber,
            note = customer.Notes,

            gender = MapGenderToString(customer.Gender),
            dob = customer.Birthdate?.ToString("yyyy-MM-dd"),
            state = customer.Status == TypeStatus.Actived ? "enabled" : "disabled",
            addresses = new[]
            {
                new
                {
                    first_name = customer.Fullname?.Split(' ').FirstOrDefault() ?? "",
                    last_name = customer.Fullname?.Contains(' ') == true
                        ? string.Join(" ", customer.Fullname.Split(' ').Skip(1))
                        : "",
                    name = customer.Fullname,
                    address1 = customer.Address,
                    city = customer.ProvinceName,
                    province = customer.DistrictName,
                    ward = customer.WardName,
                    country_code = "VN",
                    phone = customer.PhoneNumber,
                    @default = true
                }
            }
        };
    }

    private string MapGenderToString(TypeGender? gender)
    {
        return gender switch
        {
            TypeGender.Male => "male",
            TypeGender.Female => "female",
            _ => "unknown"
        };
    }

    private async Task<Result<bool>> CreateWebhooksAsync(string shopId, string webhookUrl)
    {
        try
        {
            var config = await _syncConfigRepository.FindByShopIdAndService(shopId, SyncServiceEnum.Sapo);
            if (config == null || string.IsNullOrEmpty(config.AppId) || string.IsNullOrEmpty(config.SecretKey))
            {
                return Result<bool>.Failure("Không tìm thấy cấu hình Sapo hoặc ClientId/ClientSecret");
            }

            var webhookIds = await CreateWebHooksWithBasicAuthAsync(config.AdditionalConfig, config.AppId, config.SecretKey, webhookUrl);

            if (webhookIds == null || webhookIds.Count == 0)
            {
                return Result<bool>.Failure("Không thể tạo webhook hoặc kết nối thất bại");
            }

            _logger.LogInformation("Successfully created {Count} webhooks for shop {ShopId}",
                webhookIds.Count, shopId);

            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating webhooks for shop {ShopId}", shopId);
            return Result<bool>.Failure("Có lỗi xảy ra khi tạo webhook");
        }
    }

    private async Task<List<long>> CreateWebHooksWithBasicAuthAsync(string domainApi, string clientId, string clientSecret, string webhookUrl)
    {
        try
        {
            var result = new List<long>();
            var permissions = GetDefaultPermissions();

            if (string.IsNullOrEmpty(webhookUrl) || permissions == null || permissions.Count == 0)
            {
                _logger.LogError("Missing webhook URL or permissions configuration");
                return new List<long>();
            }

            using var httpClient = _httpClientFactory.CreateClient();
            httpClient.Timeout = TimeSpan.FromSeconds(30);
            httpClient.DefaultRequestHeaders.Clear();

            // Sử dụng Basic Auth: ClientId:ClientSecret
            var basicAuthToken = Convert.ToBase64String(Encoding.UTF8.GetBytes($"{clientId}:{clientSecret}"));
            httpClient.DefaultRequestHeaders.Add("Authorization", $"Basic {basicAuthToken}");

            // URL với Basic Auth trong URL (backup method)
            var apiUrlTemplate = $"https://{clientId}:{clientSecret}@{domainApi}/admin/webhooks.json";

            _logger.LogInformation("Creating webhooks for domain: {Domain} with {Count} permissions using Basic Auth",
                domainApi, permissions.Count);

            foreach (var permission in permissions)
            {
                try
                {
                    var webhookData = new
                    {
                        webhook = new
                        {
                            topic = permission,
                            address = webhookUrl,
                            format = "json",
                            api_version = "2023-01"
                        }
                    };

                    var jsonContent = JsonConvert.SerializeObject(webhookData);
                    var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                    _logger.LogInformation("Creating webhook for permission: {Permission}", permission);
                    var response = await httpClient.PostAsync(apiUrlTemplate, content);
                    var responseContent = await response.Content.ReadAsStringAsync();

                    if (response.IsSuccessStatusCode)
                    {
                        var responseObj = JsonConvert.DeserializeObject<dynamic>(responseContent);
                        if (responseObj?.webhook?.id != null)
                        {
                            var webhookId = (long)responseObj.webhook.id;
                            result.Add(webhookId);
                            _logger.LogInformation("Created webhook for permission {Permission}: ID {WebhookId}",
                                permission, webhookId);
                        }
                    }
                    else
                    {
                        _logger.LogWarning("Failed to create webhook for permission {Permission}: {StatusCode} - {Response}",
                            permission, response.StatusCode, responseContent);

                        if (response.StatusCode == System.Net.HttpStatusCode.UnprocessableEntity &&
                            responseContent.Contains("already exists"))
                        {
                            var existingWebhookId = await FindExistingWebhookWithBasicAuth(httpClient, domainApi, clientId, clientSecret, permission, webhookUrl);
                            if (existingWebhookId.HasValue)
                            {
                                result.Add(existingWebhookId.Value);
                                _logger.LogInformation("Found existing webhook for permission {Permission}: ID {WebhookId}",
                                    permission, existingWebhookId.Value);
                            }
                        }
                    }

                    // Delay để tránh rate limit
                    await Task.Delay(100);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error creating webhook for permission {Permission}", permission);
                }
            }

            _logger.LogInformation("Webhook creation completed: Successfully created/found {Count} webhooks out of {Total} permissions",
                result.Count, permissions.Count);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating webhooks for domain {Domain}", domainApi);
            return new List<long>();
        }
    }

    private async Task<long?> FindExistingWebhookWithBasicAuth(HttpClient httpClient, string domainApi, string clientId, string clientSecret, string topic, string webhookUrl)
    {
        try
        {
            var listUrl = $"https://{clientId}:{clientSecret}@{domainApi}/admin/webhooks.json";
            var response = await httpClient.GetAsync(listUrl);

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var responseObj = JsonConvert.DeserializeObject<dynamic>(content);
                var webhooks = responseObj?.webhooks;

                if (webhooks != null)
                {
                    foreach (var webhook in webhooks)
                    {
                        var webhookTopic = webhook?.topic?.ToString();
                        var webhookAddress = webhook?.address?.ToString();
                        var webhookId = webhook?.id;

                        if (webhookTopic == topic && webhookAddress == webhookUrl && webhookId != null)
                        {
                            return (long)webhookId;
                        }
                    }
                }
            }
            else
            {
                _logger.LogWarning("Failed to list existing webhooks: {StatusCode}", response.StatusCode);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error finding existing webhook for topic {Topic}", topic);
        }

        return null;
    }

    private string GetDefaultWebhookUrl()
    {
        try
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext != null)
            {
                var request = httpContext.Request;
                var scheme = request.Scheme;
                var host = request.Host.Value;
                var baseUrl = $"https://{host}";

                var webhookPath = "/api/webhook/sapo";

                var fullWebhookUrl = $"{baseUrl}{webhookPath}";

                _logger.LogInformation("Generated webhook URL: {WebhookUrl}", fullWebhookUrl);
                return fullWebhookUrl;
            }

            _logger.LogWarning("HttpContext not available, using fallback webhook URL");
            return "https://localhost:5001/api/webhook/sapo";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating webhook URL, using fallback");
            return "https://localhost:5001/api/webhook/sapo";
        }
    }

    private List<string> GetDefaultPermissions()
    {
        return new List<string>
        {
            "products/create",
            "products/update",
            "products/delete",
            "orders/create",
            "orders/updated",
            "orders/paid",
            "orders/cancelled",
            "orders/fulfilled",
            "orders/partially_fulfilled",
            "orders/delete",
            "customers/create",
            "customers/update",
            "customers/delete"
        };
    }

    private async Task<List<SapoCollectionDto>> GetProductCollectionsFromSapo(long productId, string shopId)
    {
        try
        {
            var config = await GetSapoConfig(shopId);
            if (config == null || string.IsNullOrEmpty(config.AppId) || string.IsNullOrEmpty(config.SecretKey) || string.IsNullOrEmpty(config.AdditionalConfig))
            {
                return null;
            }

            using var httpClient = _httpClientFactory.CreateClient();
            httpClient.Timeout = TimeSpan.FromSeconds(30);
            httpClient.DefaultRequestHeaders.Clear();

            var basicAuthToken = Convert.ToBase64String(Encoding.UTF8.GetBytes($"{config.AppId}:{config.SecretKey}"));
            httpClient.DefaultRequestHeaders.Add("Authorization", $"Basic {basicAuthToken}");
            httpClient.DefaultRequestHeaders.Add("User-Agent", "ECommerceApp/1.0");
            httpClient.DefaultRequestHeaders.Add("Accept", "application/json");

            var collectionsUrl = $"https://{config.AdditionalConfig}/admin/custom_collections.json?product_id={productId}";

            _logger.LogInformation("Getting custom collections for product {ProductId} from: {Url}", productId, collectionsUrl);

            var collectionsResponse = await httpClient.GetAsync(collectionsUrl);


            if (!collectionsResponse.IsSuccessStatusCode)
            {
                var errorContent = await collectionsResponse.Content.ReadAsStringAsync();

                return null;
            }

            var collectionsContent = await collectionsResponse.Content.ReadAsStringAsync();


            var collectionsResult = JsonConvert.DeserializeObject<SapoCustomCollectionsResponseDto>(collectionsContent);

            if (collectionsResult?.CustomCollections == null || collectionsResult.CustomCollections.Count == 0)
            {
                _logger.LogInformation("No custom collections found for product {ProductId}", productId);
                return new List<SapoCollectionDto>();
            }


            var specificCollections = collectionsResult.CustomCollections
                .Where(c => !IsGenericCollection(c.Name))
                .ToList();

            if (specificCollections.Count > 0)
            {

                return specificCollections;
            }


            return collectionsResult.CustomCollections;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting product collections from Sapo for product {ProductId}", productId);
            return null;
        }
    }



    private bool IsGenericCollection(string collectionName)
    {
        var genericNames = new[]
        {
            "Tất cả sản phẩm",
            "All products",
            "Sản phẩm mới",
            "New products",
            "Sản phẩm khuyến mãi",
            "Sale products",
            "Featured products",
            "Frontpage"
        };

        return genericNames.Any(name =>
            collectionName.Equals(name, StringComparison.OrdinalIgnoreCase));
    }

    private async Task<List<string>> EnsureCategoryExists(SapoOmniProductDto product, string shopId)
    {
        try
        {
            _logger.LogInformation("Ensuring categories exist for product {ProductId} ({ProductName}) in shop {ShopId}",
                product.Id, product.Name, shopId);

            var categoryIds = new List<string>();

            // Try to get categories from Sapo collections first
            var sapoCollections = await GetProductCollectionsFromSapo(product.Id, shopId);

            if (sapoCollections != null && sapoCollections.Count > 0)
            {
                _logger.LogInformation("Found {Count} collections for product {ProductId}: {Collections}",
                    sapoCollections.Count, product.Id,
                    string.Join(", ", sapoCollections.Select(c => c.Name)));

                // Create/get category for each collection
                foreach (var collection in sapoCollections)
                {
                    try
                    {
                        var categoryId = await EnsureSingleCategoryExists(
                            collection.Name,
                            collection.Id.ToString(),
                            shopId,
                            SyncServiceEnum.Sapo);

                        if (!string.IsNullOrEmpty(categoryId))
                        {
                            categoryIds.Add(categoryId);
                            _logger.LogInformation("Added category {CategoryId} ({CategoryName}) for collection {CollectionId}",
                                categoryId, collection.Name, collection.Id);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error creating category for collection {CollectionName} ({CollectionId})",
                            collection.Name, collection.Id);
                    }
                }
            }

            // Fallback categories if no collections found or no categories created
            if (categoryIds.Count == 0)
            {
                _logger.LogInformation("No categories created from collections, using fallback logic for product {ProductId}", product.Id);

                // Try product_type
                if (!string.IsNullOrEmpty(product.ProductType))
                {
                    var categoryId = await EnsureSingleCategoryExists(product.ProductType, null, shopId, null);
                    if (!string.IsNullOrEmpty(categoryId))
                    {
                        categoryIds.Add(categoryId);
                        _logger.LogInformation("Added fallback category from product_type: {CategoryName}", product.ProductType);
                    }
                }

                // Try vendor if still no category
                if (categoryIds.Count == 0 && !string.IsNullOrEmpty(product.Vendor))
                {
                    var categoryId = await EnsureSingleCategoryExists(product.Vendor, null, shopId, null);
                    if (!string.IsNullOrEmpty(categoryId))
                    {
                        categoryIds.Add(categoryId);
                        _logger.LogInformation("Added fallback category from vendor: {CategoryName}", product.Vendor);
                    }
                }

                // Default category if still no category
                if (categoryIds.Count == 0)
                {
                    var defaultCategoryId = await CreateDefaultCategory(shopId);
                    if (!string.IsNullOrEmpty(defaultCategoryId))
                    {
                        categoryIds.Add(defaultCategoryId);
                        _logger.LogInformation("Added default category for product {ProductId}", product.Id);
                    }
                }
            }

            _logger.LogInformation("Total {Count} categories ensured for product {ProductId}: {CategoryIds}",
                categoryIds.Count, product.Id, string.Join(", ", categoryIds));

            return categoryIds;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error ensuring categories exist for product {ProductId}", product.Id);

            // Fallback to default category
            var defaultCategoryId = await CreateDefaultCategory(shopId);
            return !string.IsNullOrEmpty(defaultCategoryId)
                ? new List<string> { defaultCategoryId }
                : new List<string>();
        }
    }

    private async Task<string> EnsureSingleCategoryExists(string categoryName, string externalId, string shopId, SyncServiceEnum? syncService)
    {
        try
        {
            var shop = _shopRepository.FindByShopId(shopId);

            // Check if category exists in local database
            var existingCategory = await FindCategoryByNameOrExternalId(categoryName, externalId, shopId);

            if (existingCategory != null)
            {
                _logger.LogDebug("Found existing category {CategoryId} with name {CategoryName}",
                    existingCategory.CategoryId, existingCategory.CategoryName);
                return existingCategory.CategoryId;
            }
            else
            {
                // Create new category
                var newCategory = new Category
                {
                    CategoryId = Guid.NewGuid().ToString(),
                    CategoryName = categoryName,
                    ShopId = shopId,
                    CategoryType = TypeCategory.Product,
                    CategoryLevel = "1",
                    Publish = TypeCategoryPublish.Publish,
                    Created = DateTime.UtcNow,
                    Updated = DateTime.UtcNow,
                    ExternalId = externalId,
                    PartnerId = shop?.PartnerId,
                    ExternalSource = syncService
                };

                var created = await _categoryRepository.CreateCategory(newCategory);
                _logger.LogInformation("Created new category {CategoryId} with name {CategoryName}, ExternalId: {ExternalId}, Source: {Source}",
                    created.CategoryId, created.CategoryName, created.ExternalId, created.ExternalSource);
                return created.CategoryId;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error ensuring single category exists: {CategoryName}", categoryName);
            return string.Empty;
        }
    }

    private async Task<Category> FindCategoryByNameOrExternalId(string categoryName, string externalId, string shopId)
    {
        try
        {
            Category category = null;

            // First try to find by external ID if available (more reliable for synced categories)
            if (!string.IsNullOrEmpty(externalId))
            {
                category = await _categoryRepository.FindByExternalId(shopId, externalId, SyncServiceEnum.Sapo);

                if (category != null)
                {

                    return category;
                }
            }

            // If not found by external ID, try by name within the shop
            if (category == null)
            {
                category = await _categoryRepository.FindByCategoryName(categoryName);


                if (category != null)
                {
                    _logger.LogDebug("Found category by name {CategoryName}: {CategoryId}",
                        categoryName, category.CategoryId);

                    // Update external ID if we found by name but didn't have external ID
                    if (!string.IsNullOrEmpty(externalId) && string.IsNullOrEmpty(category.ExternalId))
                    {
                        category.ExternalId = externalId;
                        category.ExternalSource = SyncServiceEnum.Sapo;
                        category.Updated = DateTime.UtcNow;
                        await _categoryRepository.UpdateCategory(category);
                        _logger.LogInformation("Updated category {CategoryId} with ExternalId {ExternalId}",
                            category.CategoryId, externalId);
                    }

                    return category;
                }
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error finding category by name {CategoryName} or ExternalId {ExternalId}",
                categoryName, externalId);
            return null;
        }
    }

    private async Task<string> CreateDefaultCategory(string shopId)
    {
        try
        {
            var defaultCategoryName = "Sản phẩm SapoOmni";

            // Check if default category already exists for this shop
            var existingCategory = await _categoryRepository.FindByCategoryName(defaultCategoryName);

            if (existingCategory != null)
            {
                _logger.LogDebug("Found existing default category {CategoryId} for shop {ShopId}",
                    existingCategory.CategoryId, shopId);
                return existingCategory.CategoryId;
            }

            // Create new default category
            var newCategory = new Category
            {
                CategoryId = Guid.NewGuid().ToString(),
                CategoryName = defaultCategoryName,
                ShopId = shopId,
                CategoryType = TypeCategory.Product,
                CategoryLevel = "1",
                Publish = TypeCategoryPublish.Publish,
                Created = DateTime.UtcNow,
                Updated = DateTime.UtcNow,
                ExternalId = null,
                ExternalSource = null
            };

            var created = await _categoryRepository.CreateCategory(newCategory);
            _logger.LogInformation("Created default category {CategoryId} with name {CategoryName} for shop {ShopId}",
                created.CategoryId, created.CategoryName, shopId);
            return created.CategoryId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating default category for shop {ShopId}", shopId);
            return string.Empty;
        }
    }
    public async Task<Result<string>> CreateOrderToSapo(Order order, string shopId)
    {
        var httpClient = _httpClientFactory.CreateClient();

        var config = await GetSapoConfig(shopId);
        var sapoOrder = MapOrderToSapoOmniOrderDto(order);
        var payload = new { order = sapoOrder };
        var apiUrl = $"{config.AdditionalConfig}/admin/orders.json";
        var json = JsonConvert.SerializeObject(payload);
        var content = new StringContent(json, Encoding.UTF8, "application/json");

        var response = httpClient.PostAsync(apiUrl, content);


        var responseObj = JsonConvert.DeserializeObject<dynamic>(json);
        var sapoOrderId = responseObj?.order?.id?.ToString();

        if (!string.IsNullOrEmpty(sapoOrderId))
        {
            order.OrderOrigin = TypeOrigin.SapoOmni;
            _orderRepository.UpdateOrder(order);
        }

        return Result<string>.Success(sapoOrderId ?? "");

    }

    public async Task<Result<bool>> UpdateOrderToSapo(Order order, string shopId)
    {
        using var httpClient = _httpClientFactory.CreateClient();

        var config = await GetSapoConfig(shopId);
        var sapoOrder = MapOrderToSapoOmniOrderDto(order);
        var payload = new { order = sapoOrder };
        var apiUrl = $"{config.AdditionalConfig}/admin/orders/{order.ExternalId}.json";
        var json = JsonConvert.SerializeObject(payload);
        var content = new StringContent(json, Encoding.UTF8, "application/json");

        var response = httpClient.PutAsync(apiUrl, content);


        if (!response.Result.IsSuccessStatusCode)
        {

            _logger.LogError("Failed to update order in SapoOmni: {StatusCode} - {Response}", response.Result.StatusCode, json);
            return Result<bool>.Failure($"Cập nhật đơn sang SapoOmni thất bại: {response.Result.StatusCode}");
        }

        return Result<bool>.Success(true);


    }
    private object MapOrderToSapoOmniOrderDto(Order order)
    {
        return new
        {
            code = order.OrderNo,
            note = order.Notes,
            customer = new
            {
                name = order.UserShippingAddress?.FullName,
                phone_number = order.UserShippingAddress?.PhoneNumber,
            },
            billing_address = new
            {
                full_name = order.UserShippingAddress?.FullName,
                phone_number = order.UserShippingAddress?.PhoneNumber,
                address1 = order.UserShippingAddress?.Address,
                ward = order.UserShippingAddress?.WardName,
                district = order.UserShippingAddress?.DistrictName,
                city = order.UserShippingAddress?.ProvinceName,
                country = "Việt Nam"
            },
            shipping_address = new
            {
                full_name = order.UserShippingAddress?.FullName,
                phone_number = order.UserShippingAddress?.PhoneNumber,
                address1 = order.UserShippingAddress?.Address,
                ward = order.UserShippingAddress?.WardName,
                district = order.UserShippingAddress?.DistrictName,
                city = order.UserShippingAddress?.ProvinceName,
                country = "Việt Nam"
            },
            order_line_items = order.ListItems?.Select(item => new
            {
                sku = item.ItemsCode,
                product_name = item.ItemsName,
                quantity = item.Quantity,
                price = item.Price,
                note = item.Note
            }).ToList(),
            delivery_fee = order.TransportPrice,
            status = MapOrderStatusToSapoOmni(order.StatusOrder)
        };
    }
    private string MapOrderStatusToSapoOmni(TypeOrderStatus status)
    {
        return status switch
        {
            TypeOrderStatus.Pending => "draft",
            TypeOrderStatus.Verified => "open",
            TypeOrderStatus.Paid => "finalized",
            TypeOrderStatus.Success => "closed",
            TypeOrderStatus.Failed => "cancelled",
            TypeOrderStatus.Refund => "cancelled",
            _ => "draft"
        };
    }


}
