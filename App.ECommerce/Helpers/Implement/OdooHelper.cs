using App.ECommerce.Helpers.Interface;
using App.ECommerce.ProcessFlow.Interface;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Resource.Dtos;
using App.ECommerce.Resource.Dtos.OdooDtos;
using App.ECommerce.Units.Abstractions.Entities;
using App.ECommerce.Units.Enums;

using AutoMapper;

using log4net;
using System.Net.Http;
using Newtonsoft.Json;
using App.ECommerce.Resource.Model;
using App.ECommerce.Units;
using App.Base.Utilities;
namespace App.ECommerce.Helpers;

public class OdooHelper : IOdooHelper
{
    private readonly ILog _log = log4net.LogManager.GetLogger(typeof(OdooHelper));
    private readonly HttpClient _httpClient;
    protected readonly ICryptoRepository _cryptoRepository;
    protected readonly ISyncServiceConfigRepository _syncConfigRepository;
    protected readonly ISyncServiceHelper _syncServiceHelper;
    protected readonly IMapper _mapper;
    protected readonly IItemsRepository _itemsRepository;
    protected readonly IOrderRepository _orderRepository;
    protected readonly ICategoryRepository _categoryRepository;
    protected readonly IShopRepository _shopRepository;
    protected readonly IUserRepository _userRepository;
    protected readonly IWarehouseRepository _warehouseRepository;
    protected readonly IItemsFlow _itemsFlow;

    public OdooHelper(
        HttpClient httpClient,
        ICryptoRepository cryptoRepository,
        ISyncServiceConfigRepository syncConfigRepository,
        ISyncServiceHelper syncServiceHelper,
        IMapper mapper,
        IItemsRepository itemsRepository,
        IOrderRepository orderRepository,
        ICategoryRepository categoryRepository,
        IShopRepository shopRepository,
        IUserRepository userRepository,
        IWarehouseRepository warehouseRepository,
        IItemsFlow itemsFlow
    )
    {
        _httpClient = httpClient;
        _cryptoRepository = cryptoRepository;
        _syncConfigRepository = syncConfigRepository;
        _syncServiceHelper = syncServiceHelper;
        _mapper = mapper;
        _itemsRepository = itemsRepository;
        _orderRepository = orderRepository;
        _categoryRepository = categoryRepository;
        _shopRepository = shopRepository;
        _userRepository = userRepository;
        _warehouseRepository = warehouseRepository;
        _itemsFlow = itemsFlow;
    }

    public async Task<Result<SyncServiceConfig>> SaveOdooConfig(SyncServiceConfigDto dto)
    {
        try
        {
            var requestId = Guid.NewGuid().ToString();

            var config = new SyncServiceConfig
            {
                ShopId = dto.ShopId,
                SyncService = SyncServiceEnum.Odoo,
                Status = TypeStatus.Actived,
                AdditionalConfig = dto.DomainApi,
                AccessToken = dto.AccessToken
            };

            var stores = await GetStoresFromOdoo(requestId, config);
            if (stores == null)
                return Result<SyncServiceConfig>.Failure("SYNC_CONFIG_NOT_FOUND", "Odoo");

            var savedConfig = await _syncConfigRepository.CreateOrUpdate(config);
            return Result<SyncServiceConfig>.Success(savedConfig);
        }
        catch (Exception ex)
        {
            _log.Error($"SaveOdooConfig: {ex.Message}", ex);
            return Result<SyncServiceConfig>.Failure("SYNC_CONFIG_SAVE_ERROR", "Odoo");
        }
    }

    public async Task<SyncServiceConfig> GetOdooConfig(string shopId)
    {
        var config = await _syncConfigRepository.FindByShopIdAndService(shopId, SyncServiceEnum.Odoo);
        return config;
    }

    public async Task<Result<bool>> DeleteOdooConfig(string shopId)
    {
        try
        {
            var config = await GetOdooConfig(shopId);
            if (config == null) return Result<bool>.Failure("SYNC_CONFIG_NOT_FOUND", "Odoo");

            var deleted = await _syncConfigRepository.DeleteByShopIdAndService(shopId, SyncServiceEnum.Odoo);
            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            _log.Error($"DeleteOdooConfig: {ex.Message}", ex);

            return Result<bool>.Failure("SYNC_CONFIG_DELETE_ERROR", "Odoo");
        }
    }

    public async Task<Result<bool>> SyncOdooProduct(string shopId)
    {
        try {
            var config = await GetOdooConfig(shopId);
            if (config == null) 
            {
                _log.Error($"SyncOdooProduct: Odoo config not found for shopId: {shopId}");
                return Result<bool>.Failure("SYNC_CONFIG_NOT_FOUND", "ODOO");
            }

            const int pageSize = 200;
            int pageNumber = 1;
            int totalSyncedProducts = 0;
            int failedProducts = 0;
            bool hasMoreData = true;
            var startTime = DateTime.Now;

            _log.Info($"SyncOdooProduct: Bắt đầu đồng bộ sản phẩm cho shopId: {shopId}");

            while (hasMoreData)
            {
                _log.Info($"SyncOdooProduct: Đang lấy trang {pageNumber} với {pageSize} sản phẩm");
                
                var productsResponse = await GetProductsFromOdoo(config, pageNumber, pageSize);
                
                if (productsResponse == null || productsResponse.Datas == null || !productsResponse.Datas.Any())
                {
                    _log.Info($"SyncOdooProduct: Không còn dữ liệu sản phẩm ở trang {pageNumber}");
                    hasMoreData = false;
                    break;
                }

                var productsInPage = productsResponse.Datas.Count;
                _log.Info($"SyncOdooProduct: Nhận được {productsInPage} sản phẩm từ trang {pageNumber}");

                // Tối ưu hóa: Lấy tất cả external IDs một lần để tránh query database nhiều lần
                var externalIds = productsResponse.Datas.Select(p => p.ProductId.ToString()).ToList();
                var existingItems = _itemsRepository.FindByExternalIdsAndShopId(shopId, externalIds, SyncServiceEnum.Odoo);
                var existingItemsDict = existingItems.ToDictionary(x => x.ExternalId);

                var productsToCreate = new List<Items>();
                var productsToUpdate = new List<Items>();

                foreach (var product in productsResponse.Datas)
                {
                    var productDto = MapOdooProductDetailToProductDto(product, shopId, []);
                    var externalId = product.ProductId.ToString();
                    
                    if (existingItemsDict.TryGetValue(externalId, out var existingItem))
                    {
                        _mapper.Map(productDto, existingItem);
                        productsToUpdate.Add(existingItem);
                    }
                    else
                    {
                        var newItem = _mapper.Map<Items>(productDto);
                        newItem.ExternalId = externalId;
                        productsToCreate.Add(newItem);
                    }
                }

                if (productsToCreate.Any())
                    await _itemsRepository.CreateItemsBatch(productsToCreate);
                if (productsToUpdate.Any())
                    await _itemsRepository.UpdateItemsBatch(productsToUpdate);

                totalSyncedProducts += productsToCreate.Count + productsToUpdate.Count;

                // Kiểm tra xem có còn dữ liệu không
                if (productsInPage < pageSize)
                {
                    _log.Info($"SyncOdooProduct: Đã đến trang cuối (chỉ có {productsInPage} sản phẩm < {pageSize})");
                    hasMoreData = false;
                }
                else
                {
                    pageNumber++;
                    
                    await Task.Delay(50);
                }
            }

            var endTime = DateTime.Now;
            var duration = endTime - startTime;
            _log.Info($"SyncOdooProduct: Hoàn thành đồng bộ {totalSyncedProducts} sản phẩm thành công, {failedProducts} sản phẩm thất bại trong {duration.TotalSeconds:F2} giây cho shopId: {shopId}");
            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            _log.Error($"SyncOdooProduct: {ex.Message}", ex);
            return Result<bool>.Failure("SYNC_PRODUCT_SYNC_ERROR", "ODOO");
        }
    }

    public async Task<Result<OdooStoresResponseDto>> SyncOdooStores(string shopId)
    {
        try
        {
            var requestId = Guid.NewGuid().ToString();
            var config = await GetOdooConfig(shopId);
            if (config == null)
            {
                _log.Error($"SyncOdooStores: Odoo config not found for shopId: {shopId}");
                return Result<OdooStoresResponseDto>.Failure("SYNC_CONFIG_NOT_FOUND", "Odoo");
            }

            _log.Info($"SyncOdooStores: Bắt đầu lấy danh sách stores cho shopId: {shopId}");

            var storesResponse = await GetStoresFromOdoo(requestId, config);
            
            if (storesResponse == null)
            {
                _log.Error($"SyncOdooStores: Không thể lấy dữ liệu stores cho shopId: {shopId}");
                return Result<OdooStoresResponseDto>.Failure("SYNC_STORES_FETCH_ERROR", "ODOO");
            }

            _log.Info($"SyncOdooStores: Hoàn thành lấy {storesResponse.Datas?.Count ?? 0} stores cho shopId: {shopId}");
            return Result<OdooStoresResponseDto>.Success(storesResponse);
        }
        catch (Exception ex)
        {
            _log.Error($"SyncOdooStores: {ex.Message}", ex);
            return Result<OdooStoresResponseDto>.Failure("SYNC_STORES_SYNC_ERROR", "ODOO");
        }
    }

    /// <summary>
    /// Map dữ liệu từ OdooProductDto sang ProductDto (cho sản phẩm độc lập)
    /// </summary>
    /// <param name="product">Dữ liệu sản phẩm chi tiết từ Odoo API</param>
    /// <param name="shopId">ID của shop</param>
    /// <param name="categoryIds">Danh sách category IDs</param>
    /// <returns>ProductDto đã được map</returns>
    public ProductDto MapOdooProductDetailToProductDto(OdooProductDto product, string shopId, List<string> categoryIds)
    {
        Shop? shop = _shopRepository.FindByShopId(shopId);
        if (shop == null) return null;

        var warehouse = _warehouseRepository.ListWarehouse(new Paging { PageSize = 1, PageIndex = 0 }, shop.PartnerId, shopId)
            .Result?.FirstOrDefault();

        var price = decimal.TryParse(product.ProductPrice.ToString(), out var parsedPrice) ? (long)parsedPrice : 0;

        return new ProductDto
        {
            PartnerId = shop?.PartnerId,
            ItemsName = product.ProductName,
            ItemsCode = Id64.Generator(),
            ExternalSource = SyncServiceEnum.Odoo,
            CategoryIds = categoryIds,
            Price = price,
            PriceReal = price,
            // PriceCapital = importPrice,
            Quantity = 0,
            ItemsInfo = "",
            QuantityPurchase = 0,
            // Images = imageList,
            ItemsType = TypeItems.Product,
            ShopId = shopId,
            // ItemsHeight = height,
            // ItemsWidth = width,
            // ItemsLength = length,
            // ItemsWeight = shippingWeight,
            // CustomTaxRate = vat,
            IsTop = false,
            TypePublish = TypePublish.Publish,
            IsShow = true,
            WarehouseId = warehouse?.WarehouseId,
            IsVariant = false,
            Created = DateTimes.Now(),
            ListVariant = [],
        };
    }

    /// <summary>
    /// Xử lý lưu hoặc cập nhật sản phẩm từ Odoo
    /// </summary>
    /// <param name="product">Dữ liệu sản phẩm từ Odoo</param>
    /// <param name="shopId">ID của shop</param>
    /// <returns>True nếu thành công, False nếu thất bại</returns>
    private async Task<bool> ProcessOdooProduct(OdooProductDto product, string shopId)
    {
        try
        {
            var productDto = MapOdooProductDetailToProductDto(product, shopId, []);
            if (productDto == null)
            {
                _log.Error($"ProcessOdooProduct: Không thể map dữ liệu sản phẩm {product.ProductName}");
                return false;
            }

            // Kiểm tra xem sản phẩm đã tồn tại chưa
            var existingItem = _itemsRepository.FindByExternalId(shopId, product.ProductId.ToString(), SyncServiceEnum.Odoo);
            
            if (existingItem != null)
            {
                // Cập nhật sản phẩm hiện có
                _mapper.Map(productDto, existingItem);
                _itemsRepository.UpdateItems(existingItem);
                _log.Info($"ProcessOdooProduct: Cập nhật sản phẩm {product.ProductName}");
            }
            else
            {
                // Tạo sản phẩm mới
                var newItem = _mapper.Map<Items>(productDto);
                newItem.ExternalId = product.ProductId.ToString();
                _itemsRepository.CreateItems(newItem);
                _log.Info($"ProcessOdooProduct: Tạo sản phẩm mới {product.ProductName}");
            }

            return true;
        }
        catch (Exception ex)
        {
            _log.Error($"ProcessOdooProduct: Lỗi khi xử lý sản phẩm {product.ProductName}: {ex.Message}", ex);
            return false;
        }
    }

    #region API Odoo
    private async Task<OdooProductsResponseDto> GetProductsFromOdoo(SyncServiceConfig config, int pageNumber = 1, int pageSize = 200)
    {
        string requestId = Guid.NewGuid().ToString();

        try
        {
            string accessToken = config.AccessToken;
            string requestUrl = config.AdditionalConfig;

            _log.Info($"{requestId} GetProductsFromOdoo: Requesting products from {requestUrl}/api/v1/products");

            var request = new HttpRequestMessage(HttpMethod.Get, $"{requestUrl}/api/v1/products");
            
            request.Headers.Add("Authorization", $"Bearer {accessToken}");

            var jsonContent = JsonConvert.SerializeObject(new { page_number = pageNumber, page_size = pageSize });
            request.Content = new StringContent(jsonContent, System.Text.Encoding.UTF8, "application/json");

            _log.Info($"{requestId} GetProductsFromOdoo: Request payload: {jsonContent}");

            var response = await _httpClient.SendAsync(request);

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _log.Error($"{requestId} GetProductsFromOdoo failed with status code: {response.StatusCode} - {errorContent}");
                return null;
            }

            var responseContent = await response.Content.ReadAsStringAsync();
            _log.Info($"{requestId} GetProductsFromOdoo response: {responseContent}");

            var products = JsonConvert.DeserializeObject<OdooProductsResponseDto>(responseContent);
            
            _log.Info($"{requestId} GetProductsFromOdoo: Successfully retrieved {products?.Datas?.Count ?? 0} products");

            return products;
        }
        catch (Exception ex)
        {
            _log.Error($"{requestId} GetProductsFromOdoo error: {ex.Message}", ex);
            return null;
        }
    }

    private async Task<OdooStoresResponseDto> GetStoresFromOdoo(string requestId, SyncServiceConfig config)
    {
        try
        {
            string accessToken = config.AccessToken;
            string requestUrl = config.AdditionalConfig;

            _log.Info($"{requestId} GetStoresFromOdoo: Requesting stores from {requestUrl}/api/v1/stores");

            var request = new HttpRequestMessage(HttpMethod.Get, $"{requestUrl}/api/v1/stores");
            
            request.Headers.Add("Authorization", $"Bearer {accessToken}");

            // Thêm empty body cho GET request
            var emptyData = new { };
            var jsonContent = JsonConvert.SerializeObject(emptyData);
            request.Content = new StringContent(jsonContent, System.Text.Encoding.UTF8, "application/json");

            _log.Info($"{requestId} GetStoresFromOdoo: Request payload: {jsonContent}");

            var response = await _httpClient.SendAsync(request);

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _log.Error($"{requestId} GetStoresFromOdoo failed with status code: {response.StatusCode} - {errorContent}");
                return null;
            }

            var responseContent = await response.Content.ReadAsStringAsync();
            _log.Info($"{requestId} GetStoresFromOdoo response: {responseContent}");

            var stores = JsonConvert.DeserializeObject<OdooStoresResponseDto>(responseContent);
            
            _log.Info($"{requestId} GetStoresFromOdoo: Successfully retrieved {stores?.Datas?.Count ?? 0} stores");

            return stores;
        }
        catch (Exception ex)
        {
            _log.Error($"{requestId} GetStoresFromOdoo error: {ex.Message}", ex);
            return null;
        }
    }
    #endregion
}
