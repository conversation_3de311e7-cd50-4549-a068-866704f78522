using Newtonsoft.Json;

namespace App.ECommerce.Resource.Dtos.NhanhDtos;

/// <summary>
/// Generic response wrapper cho tất cả API responses từ Nhanh.vn
/// </summary>
/// <typeparam name="T">Kiểu dữ liệu của data response</typeparam>
public class NhanhApiResponse<T>
{
    /// <summary>
    /// Mã trạng thái API (1 = thành công, khác 1 = lỗi)
    /// </summary>
    [JsonProperty("code")]
    public int Code { get; set; }

    /// <summary>
    /// Thông báo lỗi nếu có
    /// </summary>
    [JsonProperty("messages")]
    public string[] Messages { get; set; }

    /// <summary>
    /// Dữ liệu response
    /// </summary>
    [JsonProperty("data")]
    public T Data { get; set; }

    /// <summary>
    /// Kiểm tra xem API call có thành công không
    /// </summary>
    [JsonIgnore]
    public bool IsSuccess => Code == 1;

    /// <summary>
    /// Lấy thông báo lỗi đầu tiên nếu có
    /// </summary>
    [JsonIgnore]
    public string FirstErrorMessage => Messages?.FirstOrDefault() ?? "Unknown error";
}

/// <summary>
/// Response data cho API customer search
/// </summary>
public class NhanhCustomerSearchData
{
    /// <summary>
    /// Danh sách khách hàng (dạng dictionary với key là customer ID)
    /// </summary>
    [JsonProperty("customers")]
    public Dictionary<string, NhanhSyncCustomerDto> Customers { get; set; }

    /// <summary>
    /// Tổng số trang
    /// </summary>
    [JsonProperty("totalPages")]
    public int TotalPages { get; set; }

    /// <summary>
    /// Trang hiện tại
    /// </summary>
    [JsonProperty("page")]
    public int Page { get; set; }
}

/// <summary>
/// Response data cho API category
/// </summary>
public class NhanhCategoryData
{
    /// <summary>
    /// Danh sách categories
    /// </summary>
    [JsonProperty("categories")]
    public List<NhanhProductCategoryDto> Categories { get; set; }
}

/// <summary>
/// Response data cho API product
/// </summary>
public class NhanhProductData
{
    /// <summary>
    /// Danh sách sản phẩm
    /// </summary>
    [JsonProperty("products")]
    public List<NhanhProductDto> Products { get; set; }

    /// <summary>
    /// Tổng số sản phẩm
    /// </summary>
    [JsonProperty("total")]
    public int Total { get; set; }
}

/// <summary>
/// DTO cho thông tin depot từ Nhanh.vn API
/// </summary>
public class NhanhDepotDto
{
    /// <summary>
    /// ID của depot
    /// </summary>
    [JsonProperty("id")]
    public int Id { get; set; }

    /// <summary>
    /// Mã depot
    /// </summary>
    [JsonProperty("code")]
    public string Code { get; set; }

    /// <summary>
    /// Tên depot
    /// </summary>
    [JsonProperty("name")]
    public string Name { get; set; }

    /// <summary>
    /// Số điện thoại
    /// </summary>
    [JsonProperty("mobile")]
    public string Mobile { get; set; }

    /// <summary>
    /// ID thành phố
    /// </summary>
    [JsonProperty("cityId")]
    public int CityId { get; set; }

    /// <summary>
    /// Tên thành phố
    /// </summary>
    [JsonProperty("cityName")]
    public string CityName { get; set; }

    /// <summary>
    /// ID quận/huyện
    /// </summary>
    [JsonProperty("districtId")]
    public int DistrictId { get; set; }

    /// <summary>
    /// Tên quận/huyện
    /// </summary>
    [JsonProperty("districtName")]
    public string DistrictName { get; set; }

    /// <summary>
    /// ID phường/xã
    /// </summary>
    [JsonProperty("wardId")]
    public int WardId { get; set; }

    /// <summary>
    /// Tên phường/xã
    /// </summary>
    [JsonProperty("wardName")]
    public string WardName { get; set; }

    /// <summary>
    /// Địa chỉ
    /// </summary>
    [JsonProperty("address")]
    public string Address { get; set; }
}
