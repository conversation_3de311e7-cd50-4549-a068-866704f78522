using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace App.ECommerce.Resource.Dtos.OdooDtos;

public class OdooProductDto
{
    [JsonProperty("product_id")]
    public int ProductId { get; set; }

    [JsonProperty("product_name")]
    public string ProductName { get; set; }

    [JsonProperty("product_price")]
    public double ProductPrice { get; set; }

    [JsonProperty("product_attribute")]
    public List<string> ProductAttribute { get; set; }
}
