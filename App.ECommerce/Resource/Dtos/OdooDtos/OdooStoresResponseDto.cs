using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace App.ECommerce.Resource.Dtos.OdooDtos;

public class OdooStoresResponseDto
{
    [JsonProperty("code")]
    public int Code { get; set; }

    [JsonProperty("name")]
    public string Name { get; set; }

    [JsonProperty("description")]
    public string Description { get; set; }

    [JsonProperty("datas")]
    public List<OdooStoreDto> Datas { get; set; }
}
