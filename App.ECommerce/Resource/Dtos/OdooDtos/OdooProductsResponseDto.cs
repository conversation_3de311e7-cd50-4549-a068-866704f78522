using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace App.ECommerce.Resource.Dtos.OdooDtos;

public class OdooProductsResponseDto
{
    [JsonProperty("code")]
    public int Code { get; set; }

    [JsonProperty("name")]
    public string Name { get; set; }

    [JsonProperty("description")]
    public string Description { get; set; }

    [JsonProperty("datas")]
    public List<OdooProductDto> Datas { get; set; }

    [JsonProperty("total")]
    public int Total { get; set; }

    [JsonProperty("current_page")]
    public int CurrentPage { get; set; }

    [JsonProperty("page_size")]
    public int PageSize { get; set; }

    [JsonProperty("total_page")]
    public int TotalPage { get; set; }
}
